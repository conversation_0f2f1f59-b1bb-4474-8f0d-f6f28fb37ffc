# Configurações do Servidor
PORT=3000

# GitHub Models API Configuration
# Obtenha seu token em: https://github.com/settings/tokens
# Permissões necessárias: read:user, read:org (para GitHub Models)
GITHUB_TOKEN=your_github_token_here

# Configurações da IA
AI_MODEL=gpt-4o
AI_MAX_TOKENS=500
AI_TEMPERATURE=0.8

# Configurações de Rate Limiting
RATE_LIMIT_WINDOW=60000
RATE_LIMIT_MAX_REQUESTS=10

# Configurações de Cache
CACHE_MAX_SIZE=100

# Configurações de Backup
BACKUP_ENABLED=true
BACKUP_INTERVAL=300000

# Configurações de Logging
LOG_LEVEL=info
LOG_API_REQUESTS=true

# Configurações de Segurança
CORS_ORIGIN=*
TRUST_PROXY=false

# Configurações de Desenvolvimento
NODE_ENV=development
DEBUG=false
