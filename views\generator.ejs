<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerar Story - Stories Generator</title>
    <link rel="stylesheet" href="/css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-video"></i>
                Stories Generator
            </div>
            <div class="nav-upgrade">
                <a href="#" class="upgrade-btn">
                    <i class="fas fa-crown"></i>
                    Economize 30% - Plano Anual
                </a>
            </div>
        </div>
    </nav>

    <div class="container">
        <aside class="sidebar">
            <ul class="nav-menu">
                <li><a href="/"><i class="fas fa-home"></i> Dashboard</a></li>
                <li><a href="/gerador" class="active"><i class="fas fa-magic"></i> Gerar Story</a></li>
                <li><a href="/ferramentas-extras"><i class="fas fa-tools"></i> Ferramentas Extras</a></li>
                <li><a href="/historico"><i class="fas fa-history"></i> Meu Histórico</a></li>
            </ul>
        </aside>

        <main class="main-content">
            <div class="page-header">
                <h1>✨ Gerar Novo Story</h1>
                <p>Crie roteiros incríveis em segundos</p>
            </div>

            <div class="generator-container">
                <form id="generatorForm" class="generator-form">
                    <!-- Step 1: Topic -->
                    <div class="form-step active" id="step1">
                        <div class="step-header">
                            <h2>1. Sobre o que é o seu story?</h2>
                            <p>Descreva brevemente o tema ou assunto</p>
                        </div>
                        <div class="form-group">
                            <label for="topic">Tema do Story</label>
                            <input type="text" id="topic" name="topic" 
                                   placeholder="Ex: Dicas de treino em casa" 
                                   required>
                            <small>Seja específico para melhores resultados</small>
                        </div>
                        <div class="step-actions">
                            <button type="button" class="btn-primary" onclick="nextStep()">
                                Próximo <i class="fas fa-arrow-right"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Step 2: Tone -->
                    <div class="form-step" id="step2">
                        <div class="step-header">
                            <h2>2. 📢 Escolha o clima para o seu texto:</h2>
                            <p>Selecione o tom que mais combina com você</p>
                        </div>
                        <div class="tone-options">
                            <div class="tone-option" onclick="selectTone('casual')">
                                <div class="tone-icon">😊</div>
                                <div class="tone-content">
                                    <h3>Casual e descontraído</h3>
                                    <p>Linguagem informal e próxima</p>
                                </div>
                                <div class="tone-radio">
                                    <input type="radio" name="tone" value="casual" id="tone-casual">
                                </div>
                            </div>
                            <div class="tone-option" onclick="selectTone('professional')">
                                <div class="tone-icon">💼</div>
                                <div class="tone-content">
                                    <h3>Profissional e direto ao ponto</h3>
                                    <p>Linguagem formal e objetiva</p>
                                </div>
                                <div class="tone-radio">
                                    <input type="radio" name="tone" value="professional" id="tone-professional">
                                </div>
                            </div>
                            <div class="tone-option" onclick="selectTone('sales')">
                                <div class="tone-icon">🎯</div>
                                <div class="tone-content">
                                    <h3>Focado em conversão (vendas)</h3>
                                    <p>Persuasivo e com call-to-action</p>
                                </div>
                                <div class="tone-radio">
                                    <input type="radio" name="tone" value="sales" id="tone-sales">
                                </div>
                            </div>
                        </div>
                        <div class="step-actions">
                            <button type="button" class="btn-secondary" onclick="prevStep()">
                                <i class="fas fa-arrow-left"></i> Voltar
                            </button>
                            <button type="button" class="btn-primary" onclick="nextStep()">
                                Próximo <i class="fas fa-arrow-right"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Step 3: Niche -->
                    <div class="form-step" id="step3">
                        <div class="step-header">
                            <h2>3. Qual é o seu nicho?</h2>
                            <p>Isso nos ajuda a personalizar ainda mais o conteúdo</p>
                        </div>
                        <div class="form-group">
                            <label for="niche">Nicho</label>
                            <select id="niche" name="niche" required>
                                <option value="">Selecione seu nicho</option>
                                <option value="fitness">Fitness e Saúde</option>
                                <option value="beleza">Beleza e Cuidados</option>
                                <option value="negocios">Negócios e Empreendedorismo</option>
                                <option value="lifestyle">Lifestyle e Bem-estar</option>
                                <option value="educacao">Educação e Conhecimento</option>
                                <option value="tecnologia">Tecnologia</option>
                                <option value="culinaria">Culinária</option>
                                <option value="viagem">Viagem e Turismo</option>
                            </select>
                        </div>
                        <div class="step-actions">
                            <button type="button" class="btn-secondary" onclick="prevStep()">
                                <i class="fas fa-arrow-left"></i> Voltar
                            </button>
                            <button type="submit" class="btn-primary" id="generateBtn">
                                <i class="fas fa-magic"></i> Gerar Story
                            </button>
                        </div>
                    </div>
                </form>

                <!-- Progress Bar -->
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-steps">
                        <div class="progress-step active" id="progress1">1</div>
                        <div class="progress-step" id="progress2">2</div>
                        <div class="progress-step" id="progress3">3</div>
                    </div>
                </div>
            </div>

            <!-- Loading State -->
            <div class="loading-container" id="loadingContainer" style="display: none;">
                <div class="loading-content">
                    <div class="loading-spinner">
                        <div class="spinner-ring"></div>
                        <div class="spinner-ring"></div>
                        <div class="spinner-ring"></div>
                    </div>
                    <h3>🤖 Gerando seu story incrível...</h3>
                    <p>🤖 Conectando com a IA...</p>
                    <div class="loading-progress">
                        <div class="progress-bar" style="width: 0%;"></div>
                    </div>
                    <small>⏱️ Isso pode levar alguns segundos</small>
                </div>
            </div>

            <!-- Result Container -->
            <div class="result-container" id="resultContainer" style="display: none;">
                <div class="result-header">
                    <h2>🎉 Seu story está pronto!</h2>
                    <div class="result-actions">
                        <button class="btn-icon" onclick="toggleResultFavorite()" id="favoriteBtn">
                            <i class="far fa-heart"></i>
                        </button>
                        <button class="btn-icon" onclick="copyResult()" title="Copiar conteúdo">
                            <i class="fas fa-copy"></i>
                        </button>
                        <button class="btn-secondary" onclick="resetGenerator()">
                            <i class="fas fa-redo"></i> Gerar Outro
                        </button>
                    </div>
                </div>
                <div class="result-content">
                    <div class="story-output" id="storyOutput">
                        <!-- Conteúdo gerado aparecerá aqui -->
                    </div>
                </div>
                <div class="result-footer">
                    <div class="result-meta">
                        <span class="meta-tag" id="resultTopic"></span>
                        <span class="meta-tag" id="resultTone"></span>
                        <span class="meta-tag" id="resultNiche"></span>
                    </div>
                    <div class="result-cta">
                        <p>Gostou do resultado? Que tal tentar nossas <a href="/ferramentas-extras">Ferramentas Extras</a>?</p>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="/js/generator.js"></script>
</body>
</html>
