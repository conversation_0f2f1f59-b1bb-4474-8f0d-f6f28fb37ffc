// History JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initializeHistory();
});

function initializeHistory() {
    // Configurar abas
    setupTabs();
    
    // Configurar interações dos cards
    setupCardInteractions();
    
    // Configurar modal
    setupModal();
}

function setupTabs() {
    // Mostrar aba "Todos" por padrão
    showTab('all');
}

function showTab(tabName) {
    // Esconder todo o conteúdo
    document.querySelectorAll('.history-content').forEach(content => {
        content.style.display = 'none';
    });
    
    // Mostrar conteúdo selecionado
    const selectedContent = document.getElementById(`tab-${tabName}`);
    if (selectedContent) {
        selectedContent.style.display = 'block';
    }
    
    // Atualizar botões das abas
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    // Encontrar e ativar o botão correto
    const activeBtn = Array.from(document.querySelectorAll('.tab-btn')).find(btn => 
        btn.textContent.toLowerCase().includes(tabName === 'all' ? 'todos' : 'favoritos')
    );
    
    if (activeBtn) {
        activeBtn.classList.add('active');
    }
}

function setupCardInteractions() {
    // Animações hover para os cards
    const cards = document.querySelectorAll('.history-card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px)';
            this.style.boxShadow = '0 12px 30px rgba(0,0,0,0.15)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '';
        });
    });
}

function setupModal() {
    // Configurar eventos do modal
    window.onclick = function(event) {
        const modal = document.getElementById('storyModal');
        if (event.target === modal) {
            closeModal();
        }
    };
    
    // Fechar modal com ESC
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            closeModal();
        }
    });
}

function toggleFavorite(storyId) {
    const heartIcon = document.querySelector(`[onclick="toggleFavorite('${storyId}')"] i`);
    const card = heartIcon.closest('.history-card');
    
    // Animação de loading no ícone
    heartIcon.className = 'fas fa-spinner fa-spin';
    
    fetch(`/api/toggle-favorite/${storyId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Atualizar ícone
            heartIcon.className = data.isFavorite ? 'fas fa-heart' : 'far fa-heart';
            
            // Efeito visual
            if (data.isFavorite) {
                heartIcon.style.color = '#ef4444';
                heartIcon.style.animation = 'heartBeat 0.6s ease';
            } else {
                heartIcon.style.color = '';
                heartIcon.style.animation = '';
            }
            
            // Mostrar notificação
            const message = data.isFavorite ? 'Adicionado aos favoritos!' : 'Removido dos favoritos!';
            showNotification('Favoritos', message, 'success');
            
            // Se estamos na aba favoritos e o item foi removido, remover o card
            if (!data.isFavorite && document.getElementById('tab-favorites').style.display !== 'none') {
                card.style.animation = 'fadeOutScale 0.3s ease forwards';
                setTimeout(() => {
                    card.remove();
                    checkEmptyState();
                }, 300);
            }
            
        } else {
            heartIcon.className = 'far fa-heart';
            showNotification('Erro', data.message || 'Não foi possível alterar o favorito', 'error');
        }
    })
    .catch(error => {
        console.error('Erro ao alterar favorito:', error);
        heartIcon.className = 'far fa-heart';
        showNotification('Erro', 'Não foi possível alterar o favorito', 'error');
    });
}

function regenerateStory(storyId) {
    // Encontrar os dados do story original
    const card = document.querySelector(`[onclick="regenerateStory('${storyId}')"]`).closest('.history-card');
    const topic = card.querySelector('.card-title h3').textContent;
    const toneElement = card.querySelector('.tone-badge');
    const tone = toneElement ? toneElement.textContent.trim() : 'casual';
    
    // Redirecionar para o gerador com os dados preenchidos
    const params = new URLSearchParams({
        topic: topic,
        tone: tone,
        regenerate: storyId
    });
    
    window.location.href = `/gerador?${params.toString()}`;
}

function copyStory(storyId) {
    // Encontrar o conteúdo do story no histórico
    const card = document.querySelector(`[onclick="copyStory('${storyId}')"]`).closest('.history-card');
    const previewElement = card.querySelector('.story-preview');
    
    if (!previewElement) {
        showNotification('Erro', 'Conteúdo não encontrado', 'error');
        return;
    }
    
    // Obter o conteúdo completo (pode estar truncado no preview)
    let content = previewElement.textContent.trim();
    
    // Se o conteúdo está truncado, remover "..." e avisar
    if (content.endsWith('...')) {
        content = content.replace(/\.\.\.$/, '');
        showNotification('Atenção', 'Conteúdo pode estar incompleto. Use "Ver Completo" para o texto completo.', 'info');
    }
    
    // Verificar se a API Clipboard está disponível
    if (!navigator.clipboard) {
        // Fallback para navegadores mais antigos
        copyToClipboardFallback(content);
        return;
    }
    
    navigator.clipboard.writeText(content).then(() => {
        // Feedback visual
        const copyBtn = card.querySelector(`[onclick="copyStory('${storyId}')"]`);
        const originalIcon = copyBtn.querySelector('i').className;
        const originalColor = copyBtn.style.color;
        
        copyBtn.querySelector('i').className = 'fas fa-check';
        copyBtn.style.color = '#10b981';
        
        setTimeout(() => {
            copyBtn.querySelector('i').className = originalIcon;
            copyBtn.style.color = originalColor;
        }, 2000);
        
        showNotification('Copiado!', 'Conteúdo copiado para a área de transferência', 'success');
    }).catch(err => {
        console.error('Erro ao copiar:', err);
        // Tentar fallback
        copyToClipboardFallback(content);
    });
}

function copyToClipboardFallback(text) {
    try {
        // Criar elemento temporário
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.top = '-9999px';
        textArea.style.left = '-9999px';
        document.body.appendChild(textArea);
        
        // Selecionar e copiar
        textArea.focus();
        textArea.select();
        
        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);
        
        if (successful) {
            showNotification('Copiado!', 'Conteúdo copiado para a área de transferência', 'success');
        } else {
            showNotification('Erro', 'Não foi possível copiar o conteúdo', 'error');
        }
    } catch (err) {
        console.error('Erro no fallback de cópia:', err);
        showNotification('Erro', 'Não foi possível copiar o conteúdo', 'error');
    }
}

function viewFullStory(storyId) {
    // Encontrar os dados do story
    const card = document.querySelector(`[onclick="viewFullStory('${storyId}')"]`).closest('.history-card');
    const title = card.querySelector('.card-title h3').textContent;
    const content = card.querySelector('.story-preview').textContent;
    const createdAt = card.querySelector('.meta-item').textContent.replace('🗓️', '').trim();
    
    // Preencher modal
    document.getElementById('modalTitle').textContent = title;
    document.getElementById('modalContent').innerHTML = `
        <div class="full-story-content">
            <div class="story-text">${content}</div>
            <div class="story-meta">
                <small class="text-muted">Criado em: ${createdAt}</small>
            </div>
        </div>
    `;
    
    // Mostrar modal
    document.getElementById('storyModal').style.display = 'block';
    document.body.style.overflow = 'hidden';
    
    // Guardar ID para a função de cópia
    document.getElementById('storyModal').setAttribute('data-story-id', storyId);
}

function closeModal() {
    document.getElementById('storyModal').style.display = 'none';
    document.body.style.overflow = 'auto';
}

function copyModalContent() {
    const storyTextElement = document.getElementById('modalContent').querySelector('.story-text');
    if (!storyTextElement) {
        showNotification('Erro', 'Conteúdo não encontrado', 'error');
        return;
    }
    
    const content = storyTextElement.textContent.trim();
    
    // Verificar se a API Clipboard está disponível
    if (!navigator.clipboard) {
        copyToClipboardFallback(content);
        closeModal();
        return;
    }
    
    navigator.clipboard.writeText(content).then(() => {
        showNotification('Copiado!', 'Conteúdo copiado para a área de transferência', 'success');
        closeModal();
    }).catch(err => {
        console.error('Erro ao copiar:', err);
        copyToClipboardFallback(content);
        closeModal();
    });
}

function checkEmptyState() {
    const activeTab = document.querySelector('.history-content[style*="block"]') || 
                     document.querySelector('.history-content:not([style*="none"])');
    
    if (activeTab) {
        const cards = activeTab.querySelectorAll('.history-card');
        if (cards.length === 0) {
            const isAllTab = activeTab.id === 'tab-all';
            const emptyStateHTML = `
                <div class="empty-state">
                    <i class="fas fa-${isAllTab ? 'history' : 'heart'}"></i>
                    <h3>${isAllTab ? 'Nenhum histórico ainda' : 'Nenhum favorito ainda'}</h3>
                    <p>${isAllTab ? 'Seus roteiros criados aparecerão aqui' : 'Adicione stories aos favoritos clicando no ❤️'}</p>
                    <a href="${isAllTab ? '/gerador' : '/'}" class="btn-primary">
                        <i class="fas fa-${isAllTab ? 'plus' : 'arrow-left'}"></i>
                        ${isAllTab ? 'Criar Primeiro Story' : 'Voltar ao Dashboard'}
                    </a>
                </div>
            `;
            activeTab.innerHTML = emptyStateHTML;
        }
    }
}

function showNotification(title, message, type = 'info') {
    // Remover notificações existentes
    document.querySelectorAll('.notification').forEach(n => n.remove());
    
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-header">
            <div class="notification-icon">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            </div>
            <div class="notification-content">
                <strong>${title}</strong>
                <p>${message}</p>
            </div>
            <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
        </div>
    `;
    
    // Adicionar estilos se não existirem
    if (!document.querySelector('#notification-styles')) {
        const styles = document.createElement('style');
        styles.id = 'notification-styles';
        styles.textContent = `
            .notification {
                position: fixed;
                top: 100px;
                right: 20px;
                background: white;
                border-radius: 12px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                z-index: 1001;
                max-width: 400px;
                animation: slideInRight 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
                overflow: hidden;
                border-left: 4px solid #667eea;
            }
            
            .notification-success { border-left-color: #10b981; }
            .notification-error { border-left-color: #ef4444; }
            .notification-info { border-left-color: #3b82f6; }
            
            .notification-header {
                padding: 1rem;
                display: flex;
                align-items: flex-start;
                gap: 0.75rem;
            }
            
            .notification-icon {
                color: #667eea;
                font-size: 1.2rem;
                margin-top: 0.1rem;
            }
            
            .notification-success .notification-icon { color: #10b981; }
            .notification-error .notification-icon { color: #ef4444; }
            
            .notification-content {
                flex: 1;
            }
            
            .notification-content strong {
                display: block;
                color: #1f2937;
                margin-bottom: 0.25rem;
            }
            
            .notification-content p {
                color: #64748b;
                margin: 0;
                font-size: 0.9rem;
            }
            
            .notification-close {
                background: none;
                border: none;
                font-size: 1.5rem;
                cursor: pointer;
                color: #94a3b8;
                padding: 0;
                width: 24px;
                height: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                transition: all 0.2s ease;
            }
            
            .notification-close:hover {
                background: #f1f5f9;
                color: #475569;
            }
            
            @keyframes slideInRight {
                from { 
                    transform: translateX(100%) scale(0.8); 
                    opacity: 0; 
                }
                to { 
                    transform: translateX(0) scale(1); 
                    opacity: 1; 
                }
            }
            
            @keyframes fadeOutScale {
                from {
                    opacity: 1;
                    transform: scale(1);
                }
                to {
                    opacity: 0;
                    transform: scale(0.8);
                }
            }
            
            @keyframes heartBeat {
                0% { transform: scale(1); }
                25% { transform: scale(1.2); }
                50% { transform: scale(1); }
                75% { transform: scale(1.1); }
                100% { transform: scale(1); }
            }
        `;
        document.head.appendChild(styles);
    }
    
    document.body.appendChild(notification);
    
    // Remover automaticamente após 4 segundos
    setTimeout(() => {
        if (notification.parentElement) {
            notification.style.animation = 'slideOutRight 0.3s ease forwards';
            setTimeout(() => notification.remove(), 300);
        }
    }, 4000);
}

// Adicionar CSS para animação de saída
document.addEventListener('DOMContentLoaded', function() {
    const additionalStyles = document.createElement('style');
    additionalStyles.textContent = `
        @keyframes slideOutRight {
            from { 
                transform: translateX(0) scale(1); 
                opacity: 1; 
            }
            to { 
                transform: translateX(100%) scale(0.8); 
                opacity: 0; 
            }
        }
        
        .full-story-content {
            line-height: 1.6;
        }
        
        .story-text {
            background: #f8fafc;
            padding: 1.5rem;
            border-radius: 8px;
            border-left: 4px solid #667eea;
            margin-bottom: 1rem;
            white-space: pre-wrap;
        }
        
        .story-meta {
            text-align: right;
            padding-top: 1rem;
            border-top: 1px solid #e2e8f0;
        }
    `;
    document.head.appendChild(additionalStyles);
});
