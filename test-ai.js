// Teste da IA de geração de stories
const { v4: uuidv4 } = require('uuid');
const moment = require('moment');

// Sistema de IA para geração de stories - versão de teste
class StoryAI {
  constructor() {
    this.templates = {
      casual: {
        fitness: [
          "Gente, preciso falar sobre {topic}! 💪\n\nEsse assunto mudou completamente minha forma de ver o treino.\n\nO que mais me impressionou:\n✨ Resultados aparecem mais rápido\n✨ Treino fica mais prazeroso\n✨ Motivação se mantém alta\n\nQuem aqui já experimentou? Conta pra mim! 👇\n\n#fitness #treino #motivacao"
        ]
      }
    };
  }

  generateStory(topic, tone, niche) {
    try {
      console.log(`🤖 Testando geração - Topic: "${topic}", Tone: "${tone}", Niche: "${niche}"`);
      
      const normalizedTone = tone.toLowerCase().trim();
      const normalizedNiche = niche.toLowerCase().trim();
      
      const toneTemplates = this.templates[normalizedTone];
      if (!toneTemplates || !toneTemplates[normalizedNiche]) {
        return `Hoje vou falar sobre ${topic}! 🌟\n\nEsse assunto tem me chamado muito a atenção e resolvi compartilhar com vocês.\n\nO que mais me impressiona é como ${topic} pode impactar positivamente nossa rotina.\n\nVocês já tiveram experiência com isso? Contem nos comentários! 💬\n\n#${niche} #inspiracao #vida`;
      }
      
      const templates = toneTemplates[normalizedNiche];
      const selectedTemplate = templates[0]; // Usar o primeiro template para teste
      
      const story = selectedTemplate.replace(/{topic}/g, topic);
      
      console.log('✅ Story gerado com sucesso!');
      return story;
      
    } catch (error) {
      console.error('❌ Erro na geração do story:', error);
      return `Erro ao gerar story sobre ${topic}`;
    }
  }
}

// Teste da IA
console.log('🧪 Iniciando teste da IA de Stories...\n');

const storyAI = new StoryAI();

// Teste 1: Fitness Casual
console.log('📝 Teste 1: Fitness Casual');
const story1 = storyAI.generateStory('treino em casa', 'casual', 'fitness');
console.log('Resultado:');
console.log(story1);
console.log('\n' + '='.repeat(50) + '\n');

// Teste 2: Lifestyle genérico
console.log('📝 Teste 2: Lifestyle (template genérico)');
const story2 = storyAI.generateStory('meditação matinal', 'casual', 'lifestyle');
console.log('Resultado:');
console.log(story2);
console.log('\n' + '='.repeat(50) + '\n');

// Teste 3: Criação de story completo
console.log('📝 Teste 3: Criação de story completo com metadados');
const story3 = {
  id: uuidv4(),
  topic: 'alimentação saudável',
  tone: 'casual',
  niche: 'lifestyle',
  content: storyAI.generateStory('alimentação saudável', 'casual', 'lifestyle'),
  createdAt: moment().format('DD/MM/YYYY HH:mm'),
  isFavorite: false
};

console.log('Story completo:');
console.log(JSON.stringify(story3, null, 2));

console.log('\n🎉 Teste concluído! A IA está funcionando corretamente.');
