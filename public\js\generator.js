// Generator JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initializeGenerator();
});

let currentStep = 1;
let formData = {};

function initializeGenerator() {
    // Configurar formulário multi-step
    setupMultiStepForm();
    
    // Verificar se há parâmetros na URL (regeneração)
    checkURLParams();
    
    // Configurar validações
    setupValidation();
    
    // Atualizar progresso inicial
    updateProgress();
}

function setupMultiStepForm() {
    // Mostrar apenas o primeiro step
    showStep(1);
    
    // Configurar eventos dos botões
    setupStepButtons();
}

function checkURLParams() {
    const urlParams = new URLSearchParams(window.location.search);
    
    if (urlParams.has('topic')) {
        document.getElementById('topic').value = urlParams.get('topic');
    }
    
    if (urlParams.has('tone')) {
        const tone = urlParams.get('tone');
        const toneRadio = document.getElementById(`tone-${tone}`);
        if (toneRadio) {
            toneRadio.checked = true;
            selectTone(tone);
        }
    }
    
    if (urlParams.has('regenerate')) {
        // Indicar que é uma regeneração
        showNotification('Regenerando', 'Preenchendo dados do story anterior...', 'info');
    }
}

function setupStepButtons() {
    // Configurar botão "Próximo" do step 1
    const step1NextBtn = document.querySelector('#step1 .btn-primary');
    if (step1NextBtn) {
        step1NextBtn.addEventListener('click', function() {
            if (validateStep(1)) {
                nextStep();
            }
        });
    }
    
    // Configurar enter no campo de tópico
    const topicInput = document.getElementById('topic');
    if (topicInput) {
        topicInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                if (validateStep(1)) {
                    nextStep();
                }
            }
        });
    }
    
    // Configurar formulário principal
    const form = document.getElementById('generatorForm');
    if (form) {
        form.addEventListener('submit', handleFormSubmit);
    }
}

function setupValidation() {
    // Validação em tempo real
    const topicInput = document.getElementById('topic');
    if (topicInput) {
        topicInput.addEventListener('input', function() {
            clearFieldError(this);
            
            if (this.value.length > 0) {
                this.classList.add('valid');
            } else {
                this.classList.remove('valid');
            }
        });
    }
}

function nextStep() {
    if (currentStep < 3) {
        hideStep(currentStep);
        currentStep++;
        showStep(currentStep);
        updateProgress();
        
        // Animar entrada do novo step
        const activeStep = document.getElementById(`step${currentStep}`);
        activeStep.style.animation = 'slideInRight 0.3s ease';
    }
}

function prevStep() {
    if (currentStep > 1) {
        hideStep(currentStep);
        currentStep--;
        showStep(currentStep);
        updateProgress();
        
        // Animar entrada do step anterior
        const activeStep = document.getElementById(`step${currentStep}`);
        activeStep.style.animation = 'slideInLeft 0.3s ease';
    }
}

function showStep(stepNumber) {
    const step = document.getElementById(`step${stepNumber}`);
    if (step) {
        step.classList.add('active');
        step.style.display = 'block';
    }
}

function hideStep(stepNumber) {
    const step = document.getElementById(`step${stepNumber}`);
    if (step) {
        step.classList.remove('active');
        step.style.display = 'none';
    }
}

function updateProgress() {
    const progressFill = document.getElementById('progressFill');
    const progressSteps = document.querySelectorAll('.progress-step');
    
    // Atualizar barra de progresso
    const progressPercentage = (currentStep / 3) * 100;
    if (progressFill) {
        progressFill.style.width = progressPercentage + '%';
    }
    
    // Atualizar steps visuais
    progressSteps.forEach((step, index) => {
        if (index + 1 <= currentStep) {
            step.classList.add('active');
        } else {
            step.classList.remove('active');
        }
    });
}

function validateStep(stepNumber) {
    switch (stepNumber) {
        case 1:
            return validateTopic();
        case 2:
            return validateTone();
        case 3:
            return validateNiche();
        default:
            return true;
    }
}

function validateTopic() {
    const topicInput = document.getElementById('topic');
    const topic = topicInput.value.trim();
    
    clearFieldError(topicInput);
    
    if (topic.length === 0) {
        showFieldError(topicInput, 'Por favor, insira um tema para o story');
        return false;
    }
    
    if (topic.length < 3) {
        showFieldError(topicInput, 'O tema deve ter pelo menos 3 caracteres');
        return false;
    }
    
    formData.topic = topic;
    return true;
}

function validateTone() {
    const selectedTone = document.querySelector('input[name="tone"]:checked');
    
    if (!selectedTone) {
        showNotification('Erro', 'Por favor, selecione um tom para o story', 'error');
        return false;
    }
    
    formData.tone = selectedTone.value;
    return true;
}

function validateNiche() {
    const nicheSelect = document.getElementById('niche');
    const niche = nicheSelect.value;
    
    clearFieldError(nicheSelect);
    
    if (!niche) {
        showFieldError(nicheSelect, 'Por favor, selecione seu nicho');
        return false;
    }
    
    formData.niche = niche;
    return true;
}

function showFieldError(field, message) {
    field.classList.add('error');
    
    // Remover erro anterior
    const existingError = field.parentNode.querySelector('.field-error');
    if (existingError) {
        existingError.remove();
    }
    
    // Adicionar nova mensagem de erro
    const errorElement = document.createElement('div');
    errorElement.className = 'field-error';
    errorElement.textContent = message;
    field.parentNode.appendChild(errorElement);
    
    // Scroll para o campo com erro
    field.scrollIntoView({ behavior: 'smooth', block: 'center' });
    field.focus();
}

function clearFieldError(field) {
    field.classList.remove('error');
    const errorElement = field.parentNode.querySelector('.field-error');
    if (errorElement) {
        errorElement.remove();
    }
}

function selectTone(tone) {
    // Remover seleção anterior
    document.querySelectorAll('.tone-option').forEach(option => {
        option.classList.remove('selected');
    });
    
    // Selecionar nova opção
    const selectedOption = event.currentTarget;
    selectedOption.classList.add('selected');
    
    // Marcar radio button
    const radio = document.getElementById(`tone-${tone}`);
    if (radio) {
        radio.checked = true;
    }
    
    // Adicionar animação de seleção
    selectedOption.style.animation = 'pulse 0.3s ease';
    
    // Salvar seleção
    formData.tone = tone;
}

function handleFormSubmit(event) {
    event.preventDefault();
    
    if (!validateStep(3)) {
        return;
    }
    
    // Mostrar loading
    showLoading();
    
    // Enviar dados para o servidor
    generateStory(formData);
}

function showLoading() {
    document.querySelector('.generator-container').style.display = 'none';
    document.getElementById('loadingContainer').style.display = 'block';
    
    // Animação de loading com frases motivacionais
    const messages = [
        'Analisando seu tema...',
        'Aplicando o tom escolhido...',
        'Personalizando para seu nicho...',
        'Finalizando seu story incrível...'
    ];
    
    let messageIndex = 0;
    const messageElement = document.querySelector('#loadingContainer p');
    
    const messageInterval = setInterval(() => {
        messageIndex = (messageIndex + 1) % messages.length;
        messageElement.textContent = messages[messageIndex];
    }, 1500);
    
    // Guardar interval para limpar depois
    window.loadingInterval = messageInterval;
}

function hideLoading() {
    document.getElementById('loadingContainer').style.display = 'none';
    
    if (window.loadingInterval) {
        clearInterval(window.loadingInterval);
    }
}

function generateStory(data) {
    fetch('/api/generate-story', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        hideLoading();
        
        if (result.success) {
            showResult(result.story);
        } else {
            showNotification('Erro', 'Não foi possível gerar o story. Tente novamente.', 'error');
            resetToStep3();
        }
    })
    .catch(error => {
        console.error('Erro ao gerar story:', error);
        hideLoading();
        showNotification('Erro', 'Erro de conexão. Verifique sua internet e tente novamente.', 'error');
        resetToStep3();
    });
}

function showResult(story) {
    const resultContainer = document.getElementById('resultContainer');
    const storyOutput = document.getElementById('storyOutput');
    
    // Preencher resultado
    storyOutput.textContent = story.content;
    
    // Preencher meta tags
    document.getElementById('resultTopic').textContent = story.topic;
    document.getElementById('resultTone').textContent = story.tone;
    document.getElementById('resultNiche').textContent = story.niche;
    
    // Mostrar container
    resultContainer.style.display = 'block';
    resultContainer.scrollIntoView({ behavior: 'smooth' });
    
    // Guardar dados do story para outras funções
    window.currentStory = story;
    
    // Animação de entrada
    resultContainer.style.animation = 'fadeInUp 0.6s ease';
    
    // Celebração de conclusão
    setTimeout(() => {
        showCelebration();
    }, 1000);
}

function showCelebration() {
    // Efeito de confete simples
    const celebration = document.createElement('div');
    celebration.innerHTML = '🎉✨🎊';
    celebration.style.cssText = `
        position: fixed;
        top: 20%;
        left: 50%;
        transform: translateX(-50%);
        font-size: 3rem;
        z-index: 1000;
        animation: celebrationBounce 2s ease;
        pointer-events: none;
    `;
    
    document.body.appendChild(celebration);
    
    setTimeout(() => {
        celebration.remove();
    }, 2000);
    
    // Adicionar CSS da animação
    if (!document.querySelector('#celebration-styles')) {
        const styles = document.createElement('style');
        styles.id = 'celebration-styles';
        styles.textContent = `
            @keyframes celebrationBounce {
                0% { transform: translateX(-50%) scale(0) rotate(0deg); opacity: 0; }
                50% { transform: translateX(-50%) scale(1.2) rotate(180deg); opacity: 1; }
                100% { transform: translateX(-50%) scale(1) rotate(360deg); opacity: 0; }
            }
        `;
        document.head.appendChild(styles);
    }
}

function resetToStep3() {
    document.querySelector('.generator-container').style.display = 'block';
    // Manter no step 3 para correção
}

function resetGenerator() {
    // Limpar formulário
    document.getElementById('generatorForm').reset();
    
    // Resetar steps
    currentStep = 1;
    formData = {};
    
    // Limpar seleções visuais
    document.querySelectorAll('.tone-option').forEach(option => {
        option.classList.remove('selected');
    });
    
    // Mostrar primeiro step
    hideStep(2);
    hideStep(3);
    showStep(1);
    updateProgress();
    
    // Esconder resultado
    document.getElementById('resultContainer').style.display = 'none';
    document.querySelector('.generator-container').style.display = 'block';
    
    // Scroll para o topo
    document.querySelector('.generator-container').scrollIntoView({ behavior: 'smooth' });
    
    showNotification('Resetado', 'Pronto para gerar um novo story!', 'info');
}

function toggleResultFavorite() {
    if (!window.currentStory) return;
    
    const favoriteBtn = document.getElementById('favoriteBtn');
    const icon = favoriteBtn.querySelector('i');
    
    // Animação de loading
    icon.className = 'fas fa-spinner fa-spin';
    
    fetch(`/api/toggle-favorite/${window.currentStory.id}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            icon.className = data.isFavorite ? 'fas fa-heart' : 'far fa-heart';
            
            if (data.isFavorite) {
                icon.style.color = '#ef4444';
                favoriteBtn.style.animation = 'heartBeat 0.6s ease';
            } else {
                icon.style.color = '';
                favoriteBtn.style.animation = '';
            }
            
            const message = data.isFavorite ? 'Adicionado aos favoritos!' : 'Removido dos favoritos!';
            showNotification('Favoritos', message, 'success');
        }
    })
    .catch(error => {
        console.error('Erro ao alterar favorito:', error);
        icon.className = 'far fa-heart';
        showNotification('Erro', 'Não foi possível alterar o favorito', 'error');
    });
}

function copyResult() {
    if (!window.currentStory) {
        showNotification('Erro', 'Nenhum story encontrado para copiar', 'error');
        return;
    }
    
    const content = window.currentStory.content;
    
    // Verificar se a API Clipboard está disponível
    if (!navigator.clipboard) {
        copyToClipboardFallback(content);
        return;
    }
    
    navigator.clipboard.writeText(content).then(() => {
        const copyBtn = document.querySelector('[onclick="copyResult()"]');
        if (copyBtn) {
            const originalIcon = copyBtn.querySelector('i').className;
            const originalBg = copyBtn.style.background;
            
            copyBtn.querySelector('i').className = 'fas fa-check';
            copyBtn.style.background = '#10b981';
            
            setTimeout(() => {
                copyBtn.querySelector('i').className = originalIcon;
                copyBtn.style.background = originalBg;
            }, 2000);
        }
        
        showNotification('Copiado!', 'Story copiado para a área de transferência', 'success');
    }).catch(err => {
        console.error('Erro ao copiar:', err);
        copyToClipboardFallback(content);
    });
}

function copyToClipboardFallback(text) {
    try {
        // Criar elemento temporário
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.top = '-9999px';
        textArea.style.left = '-9999px';
        document.body.appendChild(textArea);
        
        // Selecionar e copiar
        textArea.focus();
        textArea.select();
        
        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);
        
        if (successful) {
            const copyBtn = document.querySelector('[onclick="copyResult()"]');
            if (copyBtn) {
                const originalIcon = copyBtn.querySelector('i').className;
                const originalBg = copyBtn.style.background;
                
                copyBtn.querySelector('i').className = 'fas fa-check';
                copyBtn.style.background = '#10b981';
                
                setTimeout(() => {
                    copyBtn.querySelector('i').className = originalIcon;
                    copyBtn.style.background = originalBg;
                }, 2000);
            }
            
            showNotification('Copiado!', 'Story copiado para a área de transferência', 'success');
        } else {
            showNotification('Erro', 'Não foi possível copiar o story', 'error');
        }
    } catch (err) {
        console.error('Erro no fallback de cópia:', err);
        showNotification('Erro', 'Não foi possível copiar o story', 'error');
    }
}

function showNotification(title, message, type = 'info') {
    // Remover notificações existentes
    document.querySelectorAll('.notification').forEach(n => n.remove());
    
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-header">
            <div class="notification-icon">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            </div>
            <div class="notification-content">
                <strong>${title}</strong>
                <p>${message}</p>
            </div>
            <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
        </div>
    `;
    
    // Adicionar estilos se não existirem
    if (!document.querySelector('#notification-styles')) {
        const styles = document.createElement('style');
        styles.id = 'notification-styles';
        styles.textContent = `
            .notification {
                position: fixed;
                top: 100px;
                right: 20px;
                background: white;
                border-radius: 12px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                z-index: 1001;
                max-width: 400px;
                animation: slideInRight 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
                overflow: hidden;
                border-left: 4px solid #667eea;
            }
            
            .notification-success { border-left-color: #10b981; }
            .notification-error { border-left-color: #ef4444; }
            
            .notification-header {
                padding: 1rem;
                display: flex;
                align-items: flex-start;
                gap: 0.75rem;
            }
            
            .notification-icon {
                color: #667eea;
                font-size: 1.2rem;
                margin-top: 0.1rem;
            }
            
            .notification-success .notification-icon { color: #10b981; }
            .notification-error .notification-icon { color: #ef4444; }
            
            .notification-content {
                flex: 1;
            }
            
            .notification-content strong {
                display: block;
                color: #1f2937;
                margin-bottom: 0.25rem;
            }
            
            .notification-content p {
                color: #64748b;
                margin: 0;
                font-size: 0.9rem;
            }
            
            .notification-close {
                background: none;
                border: none;
                font-size: 1.5rem;
                cursor: pointer;
                color: #94a3b8;
                padding: 0;
                width: 24px;
                height: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                transition: all 0.2s ease;
            }
            
            .notification-close:hover {
                background: #f1f5f9;
                color: #475569;
            }
            
            @keyframes slideInRight {
                from { 
                    transform: translateX(100%) scale(0.8); 
                    opacity: 0; 
                }
                to { 
                    transform: translateX(0) scale(1); 
                    opacity: 1; 
                }
            }
            
            @keyframes slideInLeft {
                from { transform: translateX(-30px); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            
            @keyframes fadeInUp {
                from { transform: translateY(30px); opacity: 0; }
                to { transform: translateY(0); opacity: 1; }
            }
            
            @keyframes pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.05); }
                100% { transform: scale(1); }
            }
            
            @keyframes heartBeat {
                0% { transform: scale(1); }
                25% { transform: scale(1.2); }
                50% { transform: scale(1); }
                75% { transform: scale(1.1); }
                100% { transform: scale(1); }
            }
            
            .field-error {
                color: #ef4444;
                font-size: 0.85rem;
                margin-top: 0.5rem;
                display: flex;
                align-items: center;
                gap: 0.25rem;
            }
            
            .field-error:before {
                content: "⚠️";
            }
            
            input.error,
            select.error {
                border-color: #ef4444;
                box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
            }
            
            input.valid {
                border-color: #10b981;
            }
        `;
        document.head.appendChild(styles);
    }
    
    document.body.appendChild(notification);
    
    // Remover automaticamente após 4 segundos
    setTimeout(() => {
        if (notification.parentElement) {
            notification.style.animation = 'slideOutRight 0.3s ease forwards';
            setTimeout(() => notification.remove(), 300);
        }
    }, 4000);
}
