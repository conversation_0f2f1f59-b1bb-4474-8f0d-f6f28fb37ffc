#!/usr/bin/env node

/**
 * Health Check Script
 * Verifica se o servidor está funcionando corretamente
 */

const http = require('http');
const fs = require('fs');
const path = require('path');

const config = {
  host: process.env.HOST || 'localhost',
  port: process.env.PORT || 3000,
  timeout: 5000
};

console.log('🏥 Iniciando verificação de saúde...\n');

// Verificar se o servidor está respondendo
function checkServer() {
  return new Promise((resolve, reject) => {
    const req = http.request({
      hostname: config.host,
      port: config.port,
      path: '/health',
      method: 'GET',
      timeout: config.timeout
    }, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const health = JSON.parse(data);
          resolve({ status: res.statusCode, data: health });
        } catch (error) {
          reject(new Error('Resposta inválida do servidor'));
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Timeout na conexão'));
    });
    
    req.end();
  });
}

// Verificar arquivos críticos
function checkFiles() {
  const criticalFiles = [
    'server.js',
    'package.json',
    '.env.example',
    'data.json'
  ];
  
  const results = [];
  
  for (const file of criticalFiles) {
    const filePath = path.join(__dirname, '..', file);
    const exists = fs.existsSync(filePath);
    
    results.push({
      file,
      exists,
      status: exists ? '✅' : '❌'
    });
  }
  
  return results;
}

// Verificar dependências
function checkDependencies() {
  try {
    const packageJson = require('../package.json');
    const nodeModulesPath = path.join(__dirname, '..', 'node_modules');
    const nodeModulesExists = fs.existsSync(nodeModulesPath);
    
    return {
      packageJson: !!packageJson,
      nodeModules: nodeModulesExists,
      dependencies: Object.keys(packageJson.dependencies || {}).length,
      devDependencies: Object.keys(packageJson.devDependencies || {}).length
    };
  } catch (error) {
    return {
      error: error.message
    };
  }
}

// Executar verificações
async function runHealthCheck() {
  console.log('1️⃣ Verificando arquivos críticos...');
  const fileCheck = checkFiles();
  fileCheck.forEach(({ file, status }) => {
    console.log(`   ${status} ${file}`);
  });
  
  const missingFiles = fileCheck.filter(f => !f.exists);
  if (missingFiles.length > 0) {
    console.log(`\n❌ ${missingFiles.length} arquivo(s) crítico(s) ausente(s)!`);
  } else {
    console.log('\n✅ Todos os arquivos críticos presentes');
  }
  
  console.log('\n2️⃣ Verificando dependências...');
  const depCheck = checkDependencies();
  if (depCheck.error) {
    console.log(`❌ Erro: ${depCheck.error}`);
  } else {
    console.log(`✅ package.json: ${depCheck.packageJson ? 'OK' : 'ERRO'}`);
    console.log(`✅ node_modules: ${depCheck.nodeModules ? 'OK' : 'ERRO'}`);
    console.log(`📦 Dependências: ${depCheck.dependencies}`);
    console.log(`🛠️ Dev Dependencies: ${depCheck.devDependencies}`);
  }
  
  console.log('\n3️⃣ Verificando servidor...');
  try {
    const serverCheck = await checkServer();
    
    if (serverCheck.status === 200) {
      console.log('✅ Servidor respondendo');
      console.log(`🕐 Uptime: ${Math.floor(serverCheck.data.uptime)}s`);
      console.log(`🏷️ Versão: ${serverCheck.data.version}`);
      console.log(`🌍 Ambiente: ${serverCheck.data.environment}`);
      
      // Verificar serviços
      const services = serverCheck.data.services;
      console.log('\n📋 Status dos serviços:');
      Object.entries(services).forEach(([service, status]) => {
        const icon = status === 'OK' ? '✅' : status === 'CONFIGURED' ? '⚙️' : '⚠️';
        console.log(`   ${icon} ${service}: ${status}`);
      });
      
    } else {
      console.log(`❌ Servidor retornou status ${serverCheck.status}`);
    }
    
  } catch (error) {
    console.log(`❌ Servidor não está respondendo: ${error.message}`);
    console.log(`\n💡 Dicas para resolver:`);
    console.log(`   • Verifique se o servidor está rodando: npm start`);
    console.log(`   • Verifique a porta: ${config.port}`);
    console.log(`   • Verifique as variáveis de ambiente`);
  }
  
  console.log('\n🏁 Verificação de saúde concluída!');
}

// Executar se chamado diretamente
if (require.main === module) {
  runHealthCheck().catch(console.error);
}

module.exports = { checkServer, checkFiles, checkDependencies };
