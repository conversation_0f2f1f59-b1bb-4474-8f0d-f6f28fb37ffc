const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const moment = require('moment');

// Teste rápido para verificar se tudo funciona
console.log('Testando dependências...');

try {
    console.log('✓ Express:', typeof express);
    console.log('✓ Body Parser:', typeof bodyParser);
    console.log('✓ CORS:', typeof cors);
    console.log('✓ Path:', typeof path);
    console.log('✓ FS:', typeof fs);
    console.log('✓ UUID:', typeof uuidv4);
    console.log('✓ Moment:', typeof moment);
    
    console.log('\nTestando geração de UUID:', uuidv4());
    console.log('Testando formatação de data:', moment().format('DD/MM/YYYY HH:mm'));
    
    // Testar se o arquivo data.json existe e é válido
    const DATA_FILE = path.join(__dirname, 'data.json');
    if (fs.existsSync(DATA_FILE)) {
        const data = JSON.parse(fs.readFileSync(DATA_FILE, 'utf8'));
        console.log('✓ Data.json carregado com sucesso');
        console.log('- Histórico:', data.history.length, 'items');
        console.log('- Favoritos:', data.favorites.length, 'items');
    } else {
        console.log('⚠ Data.json não encontrado');
    }
    
    console.log('\n✅ Todos os testes passaram! O servidor deve funcionar corretamente.');
    
} catch (error) {
    console.error('❌ Erro nos testes:', error.message);
}
