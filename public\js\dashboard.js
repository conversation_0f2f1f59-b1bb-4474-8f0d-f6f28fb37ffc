// Dashboard JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
});

function initializeDashboard() {
    // Inicializar estatísticas em tempo real
    updateStats();
    
    // Atualizar desafio semanal
    updateWeeklyChallenge();
    
    // Inicializar barra de progresso
    initializeProgressBar();
    
    // Configurar tooltips e interações
    setupInteractions();
}

function updateStats() {
    // Buscar estatísticas atualizadas
    fetch('/api/user-stats')
        .then(response => response.json())
        .then(data => {
            // Atualizar contadores com animação
            animateCounters(data.stats);
            updateChallengeProgress(data.challenge);
        })
        .catch(error => console.error('Erro ao atualizar estatísticas:', error));
}

function animateCounters(stats) {
    // Animar contadores de estatísticas
    const counters = document.querySelectorAll('.stat-content h3');
    counters.forEach(counter => {
        const target = parseInt(counter.textContent.replace(/\D/g, ''));
        const increment = target / 20;
        let current = 0;
        
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                counter.textContent = formatStatValue(target, counter.textContent);
                clearInterval(timer);
            } else {
                counter.textContent = Math.floor(current);
            }
        }, 50);
    });
}

function formatStatValue(value, original) {
    if (original.includes('/')) {
        return original; // Manter formato "7/50"
    }
    return value.toString();
}

function updateChallengeProgress(challenge) {
    const progressFill = document.querySelector('.progress-fill');
    const progressText = document.querySelector('.progress-text');
    const motivationText = document.querySelector('.challenge-motivation');
    
    if (progressFill) {
        const percentage = (challenge.progress / challenge.target) * 100;
        progressFill.style.width = percentage + '%';
        
        if (challenge.completed) {
            motivationText.textContent = '🏆 Parabéns! Sua consistência é incrível!';
            motivationText.className = 'challenge-completed';
        }
    }
}

function updateWeeklyChallenge() {
    // Lógica para atualizar o desafio semanal
    const challengeElement = document.querySelector('.challenge-card');
    if (challengeElement) {
        challengeElement.addEventListener('click', function() {
            showChallengeDetails();
        });
    }
}

function showChallengeDetails() {
    // Mostrar detalhes do desafio em modal ou expandir
    const details = `
        <div class="challenge-details">
            <h3>Como completar este desafio:</h3>
            <ul>
                <li>Use o gerador de stories regularmente</li>
                <li>Experimente diferentes tons de voz</li>
                <li>Salve seus melhores resultados</li>
                <li>Compartilhe nas suas redes sociais</li>
            </ul>
        </div>
    `;
    
    showNotification('Dicas para completar o desafio', details, 'info');
}

function expandCalendar() {
    const calendarDetails = document.getElementById('calendarDetails');
    const isExpanded = calendarDetails.style.display !== 'none';
    
    if (isExpanded) {
        calendarDetails.style.display = 'none';
        document.querySelector('.calendar-widget .btn-secondary').textContent = 'Ver Mais Dicas';
    } else {
        calendarDetails.innerHTML = generateCalendarDetails();
        calendarDetails.style.display = 'block';
        document.querySelector('.calendar-widget .btn-secondary').textContent = 'Ver Menos';
    }
}

function generateCalendarDetails() {
    const calendar = {
        segunda: { 
            theme: 'Motivação Monday', 
            tip: 'Comece a semana inspirando seus seguidores',
            templates: ['Roteiro motivacional', 'História de superação'],
            bestTime: '07:00 - 09:00'
        },
        terca: { 
            theme: 'Tutorial Tuesday', 
            tip: 'Ensine algo novo e útil',
            templates: ['Passo a passo', 'Dica educativa'],
            bestTime: '12:00 - 14:00'
        },
        quarta: { 
            theme: 'Wisdom Wednesday', 
            tip: 'Compartilhe conhecimento e experiências',
            templates: ['Lição aprendida', 'Conselho profissional'],
            bestTime: '18:00 - 20:00'
        },
        quinta: { 
            theme: 'Throwback Thursday', 
            tip: 'Conte uma história do passado',
            templates: ['Antes e depois', 'Jornada pessoal'],
            bestTime: '19:00 - 21:00'
        },
        sexta: { 
            theme: 'Feel Good Friday', 
            tip: 'Celebre conquistas e momentos positivos',
            templates: ['Celebração', 'Gratidão'],
            bestTime: '16:00 - 18:00'
        },
        sabado: { 
            theme: 'Saturday Stories', 
            tip: 'Seja mais descontraído e pessoal',
            templates: ['Bastidores', 'Momento pessoal'],
            bestTime: '10:00 - 12:00'
        },
        domingo: { 
            theme: 'Sunday Reflection', 
            tip: 'Reflita sobre a semana e planeje a próxima',
            templates: ['Reflexão', 'Planejamento'],
            bestTime: '15:00 - 17:00'
        }
    };
    
    let html = '<div class="expanded-calendar">';
    Object.entries(calendar).forEach(([day, data]) => {
        html += `
            <div class="expanded-day">
                <h4>${day.charAt(0).toUpperCase() + day.slice(1)} - ${data.theme}</h4>
                <p><strong>Dica:</strong> ${data.tip}</p>
                <p><strong>Melhor horário:</strong> ${data.bestTime}</p>
                <p><strong>Templates sugeridos:</strong> ${data.templates.join(', ')}</p>
                <button class="btn-primary btn-sm" onclick="generateForDay('${day}')">
                    <i class="fas fa-magic"></i> Gerar para ${day.charAt(0).toUpperCase() + day.slice(1)}
                </button>
            </div>
        `;
    });
    html += '</div>';
    
    return html;
}

function showDayDetails(day) {
    const dayData = {
        segunda: { 
            theme: 'Motivação Monday', 
            tip: 'Comece a semana inspirando seus seguidores. Foque em mensagens positivas e energizantes.',
            suggestions: [
                'Compartilhe uma frase motivacional',
                'Conte sobre seus objetivos da semana',
                'Inspire com uma história de sucesso'
            ]
        },
        terca: { 
            theme: 'Tutorial Tuesday', 
            tip: 'Ensine algo novo e útil. Compartilhe conhecimento que agregue valor.',
            suggestions: [
                'Faça um tutorial passo a passo',
                'Ensine uma técnica do seu nicho',
                'Responda uma dúvida comum'
            ]
        },
        quarta: { 
            theme: 'Wisdom Wednesday', 
            tip: 'Compartilhe experiências e lições aprendidas.',
            suggestions: [
                'Conte uma lição que aprendeu',
                'Dê um conselho profissional',
                'Compartilhe uma reflexão pessoal'
            ]
        },
        quinta: { 
            theme: 'Throwback Thursday', 
            tip: 'Nostalgia e histórias do passado conectam com o público.',
            suggestions: [
                'Mostre um antes e depois',
                'Conte sua jornada até aqui',
                'Relembre um momento marcante'
            ]
        },
        sexta: { 
            theme: 'Feel Good Friday', 
            tip: 'Termine a semana com positividade e celebração.',
            suggestions: [
                'Celebre uma conquista da semana',
                'Agradeça aos seguidores',
                'Compartilhe algo que te deixa feliz'
            ]
        },
        sabado: { 
            theme: 'Saturday Stories', 
            tip: 'Seja mais pessoal e descontraído nos fins de semana.',
            suggestions: [
                'Mostre seus bastidores',
                'Compartilhe um momento pessoal',
                'Seja mais informal e próximo'
            ]
        },
        domingo: { 
            theme: 'Sunday Reflection', 
            tip: 'Reflexão e preparação para a nova semana.',
            suggestions: [
                'Reflita sobre a semana que passou',
                'Compartilhe planos para a próxima semana',
                'Faça uma pergunta para engajamento'
            ]
        }
    };
    
    const data = dayData[day];
    if (data) {
        showNotification(
            `${data.theme} - ${day.charAt(0).toUpperCase() + day.slice(1)}`,
            `
                <div class="day-details">
                    <p><strong>Dica:</strong> ${data.tip}</p>
                    <h4>Sugestões de conteúdo:</h4>
                    <ul>
                        ${data.suggestions.map(s => `<li>${s}</li>`).join('')}
                    </ul>
                    <div class="day-actions">
                        <button class="btn-primary" onclick="generateForDay('${day}')">
                            <i class="fas fa-magic"></i> Gerar Story para ${day.charAt(0).toUpperCase() + day.slice(1)}
                        </button>
                    </div>
                </div>
            `,
            'info'
        );
    }
}

function generateForDay(day) {
    // Redirecionar para o gerador com pre-configuração do dia
    const dayThemes = {
        segunda: 'motivação e energia para começar a semana',
        terca: 'tutorial ou dica educativa',
        quarta: 'experiência ou lição aprendida', 
        quinta: 'história do passado ou jornada pessoal',
        sexta: 'celebração ou gratidão',
        sabado: 'momento pessoal ou bastidores',
        domingo: 'reflexão sobre a semana'
    };
    
    const theme = dayThemes[day];
    const url = `/gerador?topic=${encodeURIComponent(theme)}&source=calendar`;
    window.location.href = url;
}

function toggleFavorite(storyId) {
    fetch(`/api/toggle-favorite/${storyId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Atualizar ícone do coração
            const heartIcon = document.querySelector(`[onclick="toggleFavorite('${storyId}')"] i`);
            if (heartIcon) {
                heartIcon.className = data.isFavorite ? 'fas fa-heart' : 'far fa-heart';
            }
            
            // Mostrar notificação
            const message = data.isFavorite ? 'Adicionado aos favoritos!' : 'Removido dos favoritos!';
            showNotification('Favoritos', message, 'success');
            
            // Atualizar contador de favoritos
            updateStats();
        }
    })
    .catch(error => {
        console.error('Erro ao alterar favorito:', error);
        showNotification('Erro', 'Não foi possível alterar o favorito', 'error');
    });
}

function setupInteractions() {
    // Configurar animações hover nos cards
    const cards = document.querySelectorAll('.stat-card, .activity-item, .calendar-day');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
    
    // Configurar cliques nos cards de estatísticas
    document.querySelectorAll('.stat-card').forEach(card => {
        card.addEventListener('click', function() {
            const statType = this.querySelector('.stat-icon i').className;
            showStatDetails(statType);
        });
    });
}

function showStatDetails(statType) {
    const details = {
        'fas fa-chart-line': {
            title: 'Stories Gerados',
            content: 'Você está no caminho certo! Continue criando conteúdo regularmente para manter o engajamento.'
        },
        'fas fa-fire': {
            title: 'Dias Consecutivos',
            content: 'Parabéns pela consistência! Manter uma rotina de criação de conteúdo é fundamental para o sucesso.'
        },
        'fas fa-heart': {
            title: 'Favoritos',
            content: 'Seus stories favoritos estão salvos para reutilização. Use-os como base para criar variações!'
        },
        'fas fa-bullhorn': {
            title: 'CTAs Testados',
            content: 'Experimente diferentes CTAs para descobrir quais funcionam melhor com seu público!'
        }
    };
    
    const detail = details[statType];
    if (detail) {
        showNotification(detail.title, detail.content, 'info');
    }
}

function showNotification(title, message, type = 'info') {
    // Criar elemento de notificação
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <h4>${title}</h4>
            <div>${message}</div>
        </div>
        <button class="notification-close" onclick="this.parentElement.remove()">×</button>
    `;
    
    // Adicionar estilos se não existirem
    if (!document.querySelector('#notification-styles')) {
        const styles = document.createElement('style');
        styles.id = 'notification-styles';
        styles.textContent = `
            .notification {
                position: fixed;
                top: 100px;
                right: 20px;
                background: white;
                border-radius: 8px;
                padding: 1rem;
                box-shadow: 0 8px 25px rgba(0,0,0,0.15);
                z-index: 1001;
                max-width: 400px;
                animation: slideInRight 0.3s ease;
                border-left: 4px solid #667eea;
            }
            
            .notification-success { border-left-color: #10b981; }
            .notification-error { border-left-color: #ef4444; }
            .notification-info { border-left-color: #3b82f6; }
            
            .notification-content h4 {
                margin: 0 0 0.5rem 0;
                color: #1f2937;
            }
            
            .notification-close {
                position: absolute;
                top: 0.5rem;
                right: 0.5rem;
                background: none;
                border: none;
                font-size: 1.2rem;
                cursor: pointer;
                color: #6b7280;
            }
            
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        `;
        document.head.appendChild(styles);
    }
    
    // Adicionar à página
    document.body.appendChild(notification);
    
    // Remover automaticamente após 5 segundos
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Função para copiar texto
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showNotification('Copiado!', 'Texto copiado para a área de transferência', 'success');
    }).catch(err => {
        console.error('Erro ao copiar:', err);
        showNotification('Erro', 'Não foi possível copiar o texto', 'error');
    });
}

// Atualizar estatísticas periodicamente
setInterval(updateStats, 60000); // A cada minuto

function initializeProgressBar() {
    const progressFill = document.querySelector('.progress-fill');
    if (progressFill) {
        const progressValue = progressFill.getAttribute('data-progress');
        if (progressValue) {
            // Animar a barra de progresso
            setTimeout(() => {
                progressFill.style.width = progressValue + '%';
            }, 500);
        }
    }
}
