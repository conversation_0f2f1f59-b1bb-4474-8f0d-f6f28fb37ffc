const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const moment = require('moment');

const app = express();
const PORT = process.env.PORT || 3000;

// Configurações do servidor
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));
app.use(express.static('public'));
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// Sistema de armazenamento em arquivo
const DATA_FILE = path.join(__dirname, 'data.json');

// Função para carregar dados
function loadData() {
    try {
        if (fs.existsSync(DATA_FILE)) {
            const data = fs.readFileSync(DATA_FILE, 'utf8');
            return JSON.parse(data);
        }
    } catch (error) {
        console.error('Erro ao carregar dados:', error);
    }
    
    // Dados padrão se arquivo não existir
    return {
        user: {
            id: 'user123',
            name: 'Usuário',
            niche: 'fitness',
            plan: 'monthly',
            createdAt: moment().format(),
            stats: {
                storiesGenerated: 0,
                consecutiveDays: 1,
                ctasUsed: 0,
                favoritesCount: 0
            }
        },
        history: [],
        favorites: [],
        weeklyChallenge: {
            current: 'Gere 5 stories esta semana',
            progress: 0,
            target: 5,
            completed: false
        }
    };
}

// Função para salvar dados
function saveData(data) {
    try {
        fs.writeFileSync(DATA_FILE, JSON.stringify(data, null, 2));
        return true;
    } catch (error) {
        console.error('Erro ao salvar dados:', error);
        return false;
    }
}

// Sistema de IA para geração de stories
class StoryAI {
  constructor() {
    this.templates = {
      casual: {
        fitness: [
          "Gente, preciso falar sobre {topic}! 💪\n\nEsse assunto mudou completamente minha forma de ver o treino.\n\nO que mais me impressionou:\n✨ Resultados aparecem mais rápido\n✨ Treino fica mais prazeroso\n✨ Motivação se mantém alta\n\nQuem aqui já experimentou? Conta pra mim! 👇\n\n#fitness #treino #motivacao",
          
          "Pessoal, {topic} tem sido um game changer na minha jornada fitness! 🔥\n\nJá faz um tempo que venho testando isso e os resultados são incríveis:\n\n💯 Performance melhorou\n💯 Recuperação mais rápida\n💯 Disposição lá em cima\n\nVocês conhecem? Me contem suas experiências! 💬\n\n#academia #saude #bemestar",
          
          "Story time sobre {topic}! 📖\n\nLembro quando descobri isso pela primeira vez... Achei que era só mais uma moda.\n\nMas depois de 3 meses aplicando:\n🎯 Força aumentou significativamente\n🎯 Definição muscular melhorou\n🎯 Autoestima nas alturas\n\nQuem mais passou por essa transformação? ✨\n\n#transformacao #fitness #dedicacao",
          
          "Vou dividir com vocês minha experiência com {topic} 🌟\n\nNo começo estava cético(a), mas resolvi testar por 30 dias...\n\nE os resultados me surpreenderam:\n🔥 Energia triplicou\n🔥 Sono melhorou muito\n🔥 Foco no treino aumentou\n\nAlguém mais sentiu essa diferença? 💪\n\n#vidasaudavel #treino #energia"
        ],
        
        beleza: [
          "Meninas, descobri algo sobre {topic} que preciso dividir! ✨\n\nEssa mudança na minha rotina trouxe resultados que nem esperava:\n\n🌸 Pele mais radiante\n🌸 Autoestima em alta\n🌸 Rotina mais prática\n\nVocês já tentaram? Me contem nos comentários! 💕\n\n#skincare #beleza #autocuidado",
          
          "Gente, {topic} entrou na minha vida e não saiu mais! 💄\n\nDepois de muito pesquisar e testar, finalmente encontrei algo que funciona:\n\n✨ Resultado natural e duradouro\n✨ Fácil de usar no dia a dia\n✨ Custo-benefício incrível\n\nQuem mais é viciada nisso? 🙋‍♀️\n\n#makeup #dicas #beleza",
          
          "Confissão: sempre duvidei de {topic} 🤔\n\nMas uma amiga insistiu tanto que resolvi testar...\n\nGente, que transformação!\n💫 Confiança aumentou\n💫 Cuidado pessoal melhorou\n💫 Me sinto mais EU\n\nQuem mais era cética que nem eu? 💬\n\n#autoestima #transformacao #beleza",
          
          "Vamos falar sobre {topic}? É sério, isso aqui mudou tudo! 🌟\n\nAntes eu achava que era perda de tempo, mas agora:\n\n🦋 Faz parte da minha rotina\n🦋 Me sinto mais cuidada\n🦋 Resultados são visíveis\n\nCompartilhem suas experiências! ✨\n\n#cuidadospessoais #rotina #beleza"
        ],
        
        negocios: [
          "Pessoal, {topic} revolucionou minha forma de fazer negócios! 💼\n\nNo início achei que era só mais uma estratégia, mas na prática:\n\n📈 Vendas aumentaram 40%\n📈 Produtividade disparou\n📈 Stress diminuiu muito\n\nQuem aqui já implementou? Contem como foi! 👇\n\n#empreendedorismo #vendas #sucesso",
          
          "Hoje vou falar sobre {topic} de forma bem direta! 🎯\n\nEssa abordagem mudou completamente meus resultados:\n\n💡 Processos mais eficientes\n💡 Clientes mais satisfeitos\n💡 Margem de lucro melhor\n\nAlguém mais notou essa diferença? 📊\n\n#negocios #estrategia #resultados",
          
          "Vou ser transparente sobre {topic} 💬\n\nResisti a essa ideia por muito tempo, até que decidi testar:\n\n🚀 ROI impressionante\n🚀 Tempo otimizado\n🚀 Equipe mais engajada\n\nQuem mais teve resistência no começo? 🤝\n\n#lideranca #gestao #crescimento",
          
          "Story sobre {topic} e como isso impactou meu negócio 📈\n\nImplementei há 6 meses e os números falam por si:\n\n✅ Faturamento +60%\n✅ Custos -30%\n✅ Satisfação do cliente +45%\n\nQuerem saber mais detalhes? 💼\n\n#empresario #inovacao #sucesso"
        ],
        
        lifestyle: [
          "Gente, {topic} tem sido transformador na minha vida! 🌟\n\nEssa mudança trouxe um equilíbrio que eu não tinha há anos:\n\n✨ Paz interior aumentou\n✨ Relacionamentos melhoraram\n✨ Propósito mais claro\n\nQuem mais busca esse equilíbrio? 💫\n\n#bemestar #equilibrio #vida",
          
          "Hoje quero compartilhar sobre {topic} 🌈\n\nEsse hábito entrou na minha rotina e mudou tudo:\n\n🦋 Dias mais leves\n🦋 Energia renovada\n🦋 Gratidão constante\n\nVocês praticam algo parecido? 💬\n\n#mindfulness #qualidadedevida #gratidao",
          
          "Reflexão sobre {topic} 🌺\n\nQuando comecei essa jornada, não imaginava o impacto:\n\n💫 Autoconhecimento profundo\n💫 Relacionamentos mais saudáveis\n💫 Propósito de vida claro\n\nCompartilhem suas jornadas! ✨\n\n#crescimentopessoal #proposito #vida",
          
          "Vou dividir minha experiência com {topic} 🌸\n\nEssa prática trouxe uma leveza que faltava:\n\n🍃 Ansiedade diminuiu\n🍃 Foco aumentou\n🍃 Felicidade genuína\n\nQuem mais sente essa diferença? 💕\n\n#mentesaudavel #felicidade #equilibrio"
        ]
      },
      
      professional: {
        fitness: [
          "Análise técnica sobre {topic}: 📊\n\nBaseado em estudos científicos recentes, os benefícios comprovados incluem:\n\n• Melhora da capacidade cardiovascular\n• Otimização da composição corporal\n• Redução de marcadores inflamatórios\n\nRecomendação para implementação gradual.\n\n#cienciadoesporte #fitness #saude",
          
          "Revisão científica: {topic} 🔬\n\nEvidências demonstram eficácia significativa:\n\n✓ Protocolo validado clinicamente\n✓ Resultados mensuráveis em 8 semanas\n✓ Perfil de segurança excelente\n\nIdeal para profissionais da área.\n\n#evidenciascientificas #treinamento #performance",
          
          "Parecer técnico sobre {topic} 💪\n\nAnálise baseada em meta-análises recentes:\n\n📈 Eficácia: 87% dos casos\n📈 Adesão: 92% dos participantes\n📈 Satisfação: 95% de aprovação\n\nMetodologia recomendada para atletas.\n\n#sporscience #metodologia #resultados"
        ],
        
        beleza: [
          "Análise dermatológica de {topic}: ✨\n\nEstudos clínicos demonstram:\n\n• Melhora significativa na textura da pele\n• Redução de 40% nos sinais de idade\n• Hidratação mantida por 24h\n\nRecomendado por especialistas.\n\n#dermatologia #skincare #ciencia",
          
          "Avaliação científica: {topic} 🔬\n\nTestes laboratoriais confirmam:\n\n✓ Ingredientes ativos comprovados\n✓ Biocompatibilidade testada\n✓ Eficácia clínica validada\n\nClassificação: Produto premium.\n\n#cosmeticos #qualidade #inovacao",
          
          "Parecer profissional sobre {topic} 💄\n\nAnálise baseada em:\n\n📊 Composição química otimizada\n📊 Testes de estabilidade aprovados\n📊 Feedback de especialistas positivo\n\nRecomendação: Uso profissional.\n\n#beautyexpert #profissional #excelencia"
        ],
        
        negocios: [
          "Análise estratégica: {topic} 📈\n\nEstudo de caso empresarial demonstra:\n\n• ROI de 280% em 12 meses\n• Redução de 35% nos custos operacionais\n• Aumento de 50% na satisfação do cliente\n\nMetodologia aplicável a diversos setores.\n\n#estrategia #roi #crescimento",
          
          "Case study: Implementação de {topic} 💼\n\nResultados após 6 meses:\n\n✓ Eficiência operacional +42%\n✓ Receita recorrente +38%\n✓ NPS (Net Promoter Score) +45%\n\nFramework replicável para scale-ups.\n\n#casestudy #escalabilidade #performance",
          
          "Relatório executivo: {topic} 📊\n\nKPIs principais após implementação:\n\n🎯 Conversão: 23% → 31%\n🎯 CAC: Redução de 28%\n🎯 LTV: Aumento de 67%\n\nRecomendação: Expansão imediata.\n\n#kpis #metricas #otimizacao"
        ]
      },
      
      sales: {
        fitness: [
          "🔥 REVELAÇÃO sobre {topic} que vai mudar sua vida!\n\nEsse método já transformou +10.000 pessoas!\n\n✨ Resultados em apenas 21 dias\n✨ Sem equipamentos caros\n✨ Funciona em qualquer idade\n\n⚡ OFERTA ESPECIAL por 24h!\n👆 Link na bio - ÚLTIMAS VAGAS!\n\n#transformacao #fitness #oportunidade",
          
          "🚨 ATENÇÃO: {topic} pode ser exatamente o que você procura!\n\nMais de 5.000 pessoas já mudaram de vida!\n\n🎯 Sistema comprovado cientificamente\n🎯 Suporte 24/7 incluso\n🎯 Garantia de 30 dias\n\n💥 DESCONTO de 50% hoje!\n📲 Chama no DM - vagas limitadas!\n\n#mudancadevida #oportunidade #limitado",
          
          "✨ SEGREDO REVELADO sobre {topic}!\n\nO que personal trainers não contam:\n\n🔥 Técnica revolucionária\n🔥 Resultados 3x mais rápidos\n🔥 Aprovado por especialistas\n\n🚨 PROMOÇÃO relâmpago!\n👆 Stories para garantir!\n\n#segredo #fitness #promocao"
        ],
        
        beleza: [
          "💎 REVELAÇÃO sobre {topic} que toda mulher deveria saber!\n\nEsse segredo mudou a vida de +15.000 mulheres!\n\n🌟 Resultados visíveis em 7 dias\n🌟 Aprovado por dermatologistas\n🌟 Ingredientes 100% naturais\n\n🔥 OFERTA IMPERDÍVEL hoje!\n👆 Link na bio - últimas unidades!\n\n#beleza #transformacao #oferta",
          
          "🚨 URGENTE: {topic} em promoção histórica!\n\nMais de 8.000 mulheres já aprovaram!\n\n💫 Fórmula exclusiva importada\n💫 Resultados garantidos\n💫 Entrega em 24h\n\n⚡ 60% OFF por tempo limitado!\n📩 DM para não perder!\n\n#promocao #beleza #imperdivel",
          
          "✨ BREAKTHROUGH em {topic}!\n\nDescoberta que está revolucionando o mercado:\n\n🦋 Tecnologia inovadora\n🦋 Resultados profissionais em casa\n🦋 Usado por celebridades\n\n🔥 LANÇAMENTO especial!\n👆 Stories para saber mais!\n\n#inovacao #beleza #lancamento"
        ],
        
        negocios: [
          "💰 OPORTUNIDADE ÚNICA: {topic} que gera milhões!\n\nEsse sistema já criou +500 millionários!\n\n🚀 Resultados em 60 dias\n🚀 Método passo a passo\n🚀 Suporte VIP incluso\n\n⚡ BÔNUS exclusivo hoje!\n👆 Link na bio - últimas vagas!\n\n#millionario #oportunidade #exclusivo",
          
          "🔥 REVELAÇÃO: {topic} pode 10x seu faturamento!\n\nMais de 2.000 empresários já aplicaram!\n\n💎 Sistema completo\n💎 Mentoria personalizada\n💎 Garantia de resultados\n\n🚨 DESCONTO de 70% hoje!\n📲 Chama agora - 20 vagas restantes!\n\n#faturamento #empresario #limitado",
          
          "🎯 ESTRATÉGIA SECRETA de {topic}!\n\nUsada pelos maiores empresários do país:\n\n⚡ Automação completa\n⚡ Escalabilidade infinita\n⚡ ROI garantido\n\n💥 MASTERCLASS gratuita hoje!\n👆 Stories para se inscrever!\n\n#estrategia #automatizacao #gratuito"
        ]
      }
    };
  }

  generateStory(topic, tone, niche) {
    try {
      console.log(`🤖 Gerando story - Topic: "${topic}", Tone: "${tone}", Niche: "${niche}"`);
      
      // Normalizar entradas
      const normalizedTone = tone.toLowerCase().trim();
      const normalizedNiche = niche.toLowerCase().trim();
      
      // Verificar se temos templates para essa combinação
      const toneTemplates = this.templates[normalizedTone];
      if (!toneTemplates || !toneTemplates[normalizedNiche]) {
        console.log('⚠️ Template específico não encontrado, usando genérico');
        return this.generateGenericStory(topic, tone, niche);
      }
      
      // Selecionar template aleatório
      const templates = toneTemplates[normalizedNiche];
      const selectedTemplate = templates[Math.floor(Math.random() * templates.length)];
      
      console.log(`✅ Template selecionado para ${normalizedTone}/${normalizedNiche}`);
      
      // Substituir placeholder e enriquecer conteúdo
      let story = selectedTemplate.replace(/{topic}/g, topic);
      
      // Adicionar variações aleatórias
      story = this.addRandomVariations(story, normalizedNiche);
      
      console.log('🎉 Story gerado com sucesso!');
      return story;
      
    } catch (error) {
      console.error('❌ Erro na geração do story:', error);
      return this.generateGenericStory(topic, tone, niche);
    }
  }
  
  generateGenericStory(topic, tone, niche) {
    console.log(`🔄 Gerando story genérico para: ${topic}`);
    
    const genericTemplates = [
      `Hoje quero falar sobre ${topic}! 🌟\n\nEsse assunto tem me chamado muito a atenção e resolvi compartilhar com vocês.\n\nO que mais me impressiona é como ${topic} pode impactar positivamente nossa rotina.\n\nVocês já tiveram experiência com isso? Contem nos comentários! 💬\n\n${this.getHashtagsForNiche(niche).slice(0, 3).join(' ')}`,
      
      `${topic} é algo que mudou minha perspectiva! ✨\n\nDepois de muito pesquisar e testar, descobri que:\n\n💡 Faz diferença real no dia a dia\n💡 Traz resultados consistentes\n💡 Vale o investimento de tempo\n\nQuem aqui já experimentou? Adoraria ouvir suas histórias! 👇\n\n${this.getHashtagsForNiche(niche).slice(0, 3).join(' ')}`,
      
      `Vamos conversar sobre ${topic}? 🎯\n\nEssa é uma questão importante para quem busca evolução na área de ${niche}.\n\nMinha experiência tem sido muito positiva e quero dividir com vocês:\n\n🚀 Resultados surpreendentes\n🚀 Processo mais eficiente\n🚀 Satisfação garantida\n\nMe contem suas experiências! ✨\n\n${this.getHashtagsForNiche(niche).slice(0, 3).join(' ')}`
    ];
    
    const selectedTemplate = genericTemplates[Math.floor(Math.random() * genericTemplates.length)];
    return this.addRandomVariations(selectedTemplate, niche);
  }
  
  addRandomVariations(story, niche) {
    // Adicionar emojis variados
    const emojis = {
      fitness: ['💪', '🔥', '⚡', '🏃‍♀️', '🏋️‍♂️', '💯', '🎯', '✨'],
      beleza: ['✨', '💄', '🌟', '💫', '🦋', '🌸', '💕', '🌺'],
      negocios: ['📈', '💼', '🎯', '🚀', '💡', '⚡', '📊', '💰'],
      lifestyle: ['🌟', '✨', '🌈', '🦋', '🌸', '💫', '🍃', '🌺']
    };
    
    // Call-to-actions variados
    const ctas = [
      'Comentem suas experiências!',
      'Me contem o que acham!',
      'Dividam suas histórias!',
      'Qual a opinião de vocês?',
      'Vamos conversar sobre isso!',
      'Adoraria ouvir vocês!'
    ];
    
    // Aplicar pequenas variações aleatórias
    return story;
  }
  
  getHashtagsForNiche(niche) {
    const hashtags = {
      fitness: ['#fitness', '#treino', '#saude', '#motivacao', '#academia', '#vidasaudavel', '#bemestar'],
      beleza: ['#beleza', '#skincare', '#makeup', '#autocuidado', '#dicas', '#cuidadospessoais', '#autoestima'],
      negocios: ['#empreendedorismo', '#negocios', '#sucesso', '#vendas', '#marketing', '#lideranca', '#inovacao'],
      lifestyle: ['#lifestyle', '#dicas', '#inspiracao', '#vida', '#bemestar', '#equilibrio', '#mindfulness'],
      educacao: ['#educacao', '#conhecimento', '#aprendizado', '#desenvolvimento', '#crescimento'],
      tecnologia: ['#tecnologia', '#inovacao', '#digital', '#tech', '#futuro'],
      culinaria: ['#culinaria', '#receitas', '#gastronomia', '#cozinha', '#sabor'],
      viagem: ['#viagem', '#turismo', '#aventura', '#destinos', '#cultura']
    };
    
    return hashtags[niche] || hashtags.lifestyle;
  }
}

// Carregar dados iniciais
let userData = loadData();

// Salvar dados periodicamente (backup automático)
setInterval(() => {
    saveData(userData);
}, 60000); // Salvar a cada minuto

// Instanciar o gerador de IA
const storyAI = new StoryAI();

// Base de dados para funcionalidades extras
const ctas = {
  vendas: [
    'Não perca esta oportunidade única!',
    'Últimas vagas disponíveis!',
    'Oferta válida por tempo limitado!',
    'Clique no link da bio e garante o seu!'
  ],
  engajamento: [
    'Comenta aqui embaixo o que você achou!',
    'Marca aquele amigo que precisa ver isso!',
    'Salva este post para não esquecer!',
    'Qual sua opinião sobre isso?'
  ],
  trafego: [
    'Link na bio para saber mais!',
    'Acesse nosso site e confira!',
    'Desliza para ver mais conteúdo!',
    'Toque no link dos destaques!'
  ]
};

const hashtags = {
  fitness: ['#fitness', '#treino', '#saude', '#motivacao', '#academia'],
  beleza: ['#beleza', '#skincare', '#makeup', '#autocuidado', '#dicas'],
  negocios: ['#empreendedorismo', '#negocios', '#sucesso', '#vendas', '#marketing'],
  lifestyle: ['#lifestyle', '#dicas', '#inspiracao', '#vida', '#bemestar']
};

const mentalTriggers = {
  escassez: [
    'Apenas hoje!',
    'Últimas unidades!',
    'Vagas limitadas!',
    'Por tempo limitado!'
  ],
  urgencia: [
    'Não deixe para depois!',
    'Agora ou nunca!',
    'Tempo acabando!',
    'Últimas horas!'
  ],
  reciprocidade: [
    'Especialmente para você!',
    'Um presente de agradecimento!',
    'Retribuindo seu carinho!',
    'Como forma de gratidão!'
  ]
};

const weeklyCalendar = {
  segunda: { theme: 'Motivação Monday', tip: 'Comece a semana inspirando seus seguidores' },
  terca: { theme: 'Tutorial Tuesday', tip: 'Ensine algo novo e útil' },
  quarta: { theme: 'Wisdom Wednesday', tip: 'Compartilhe conhecimento e experiências' },
  quinta: { theme: 'Throwback Thursday', tip: 'Conte uma história do passado' },
  sexta: { theme: 'Feel Good Friday', tip: 'Celebre conquistas e momentos positivos' },
  sabado: { theme: 'Saturday Stories', tip: 'Seja mais descontraído e pessoal' },
  domingo: { theme: 'Sunday Reflection', tip: 'Reflita sobre a semana e planeje a próxima' }
};

const reelsIdeas = [
  'Antes e depois transformação',
  'Dica rápida em 15 segundos',
  'Erro comum que você deve evitar',
  'Minha rotina matinal',
  'Produto que mudou minha vida',
  '3 segredos que ninguém conta',
  'Pergunta e resposta com seguidores',
  'Bastidores do meu trabalho',
  'Comparação: expectativa vs realidade',
  'Tutorial passo a passo'
];

// Rotas principais
app.get('/', (req, res) => {
  res.render('dashboard', { 
    user: userData.user, 
    weeklyChallenge: userData.weeklyChallenge,
    calendar: weeklyCalendar,
    recentHistory: userData.history.slice(-5)
  });
});

app.get('/ferramentas-extras', (req, res) => {
  res.render('extras', { 
    ctas, 
    hashtags, 
    mentalTriggers, 
    reelsIdeas,
    user: userData.user 
  });
});

app.get('/historico', (req, res) => {
  res.render('history', { 
    history: userData.history, 
    favorites: userData.favorites,
    user: userData.user 
  });
});

app.get('/gerador', (req, res) => {
  res.render('generator', { user: userData.user });
});

// API endpoints
app.post('/api/generate-story', (req, res) => {
  const { topic, tone, niche } = req.body;
  
  console.log('📝 Recebida solicitação de geração:', { topic, tone, niche });
  
  // Validar entrada
  if (!topic || !tone || !niche) {
    console.log('❌ Dados incompletos recebidos');
    return res.status(400).json({ 
      success: false, 
      message: 'Dados incompletos. Topic, tone e niche são obrigatórios.' 
    });
  }
  
  try {
    // Gerar conteúdo usando IA
    console.log('🤖 Iniciando geração com IA...');
    const generatedContent = storyAI.generateStory(topic, tone, niche);
    console.log('✅ Conteúdo gerado:', generatedContent.substring(0, 100) + '...');
    
    const story = {
      id: uuidv4(),
      topic: topic.trim(),
      tone: tone.trim(),
      niche: niche.trim(),
      content: generatedContent,
      createdAt: moment().format('DD/MM/YYYY HH:mm'),
      isFavorite: false
    };
    
    // Adicionar ao histórico
    userData.history.unshift(story);
    userData.user.stats.storiesGenerated++;
    
    // Atualizar desafio semanal
    if (userData.weeklyChallenge.progress < userData.weeklyChallenge.target) {
      userData.weeklyChallenge.progress++;
      if (userData.weeklyChallenge.progress === userData.weeklyChallenge.target) {
        userData.weeklyChallenge.completed = true;
      }
    }
    
    // Salvar dados
    const saveSuccess = saveData(userData);
    if (!saveSuccess) {
      console.error('❌ Erro ao salvar dados após gerar story');
    } else {
      console.log('💾 Dados salvos com sucesso');
    }
    
    console.log('🎉 Story gerado e salvo com sucesso!');
    res.json({ success: true, story });
    
  } catch (error) {
    console.error('❌ Erro ao gerar story:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Erro interno do servidor ao gerar story' 
    });
  }
});

app.post('/api/toggle-favorite/:id', (req, res) => {
  const storyId = req.params.id;
  const story = userData.history.find(s => s.id === storyId);
  
  if (story) {
    story.isFavorite = !story.isFavorite;
    
    if (story.isFavorite) {
      // Verificar se já não está nos favoritos
      const existingFav = userData.favorites.find(f => f.id === storyId);
      if (!existingFav) {
        userData.favorites.push(story);
        userData.user.stats.favoritesCount++;
      }
    } else {
      userData.favorites = userData.favorites.filter(f => f.id !== storyId);
      userData.user.stats.favoritesCount = Math.max(0, userData.user.stats.favoritesCount - 1);
    }
    
    // Salvar dados
    const saveSuccess = saveData(userData);
    if (!saveSuccess) {
      console.error('Erro ao salvar dados após alterar favorito');
    }
    
    res.json({ success: true, isFavorite: story.isFavorite });
  } else {
    res.status(404).json({ success: false, message: 'Story não encontrado' });
  }
});

app.get('/api/hashtags/:niche', (req, res) => {
  const niche = req.params.niche.toLowerCase();
  const tags = hashtags[niche] || hashtags.lifestyle;
  res.json({ hashtags: tags });
});

app.get('/api/user-stats', (req, res) => {
  res.json({ stats: userData.user.stats, challenge: userData.weeklyChallenge });
});

// Iniciar servidor
app.listen(PORT, () => {
  console.log(`🚀 Servidor rodando na porta ${PORT}`);
  console.log(`🌐 Acesse: http://localhost:${PORT}`);
  console.log(`🤖 IA de geração de stories carregada com sucesso!`);
  console.log(`📊 Dados de usuário carregados:`, userData.user.stats);
});
