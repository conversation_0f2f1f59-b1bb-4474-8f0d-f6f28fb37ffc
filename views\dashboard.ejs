<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stories Generator - Dashboard</title>
    <link rel="stylesheet" href="/css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-video"></i>
                Stories Generator
            </div>
            <div class="nav-upgrade">
                <a href="#" class="upgrade-btn">
                    <i class="fas fa-crown"></i>
                    Economize 30% - Plano Anual
                </a>
            </div>
        </div>
    </nav>

    <div class="container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <ul class="nav-menu">
                <li><a href="/" class="active"><i class="fas fa-home"></i> Dashboard</a></li>
                <li><a href="/gerador"><i class="fas fa-magic"></i> Gerar Story</a></li>
                <li><a href="/ferramentas-extras"><i class="fas fa-tools"></i> Ferramentas Extras</a></li>
                <li><a href="/historico"><i class="fas fa-history"></i> Meu Histórico</a></li>
            </ul>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Welcome Section -->
            <div class="welcome-section">
                <div class="welcome-text">
                    <h1>Olá, <%= user.name %>! 👋</h1>
                    <p>Pronto para criar conteúdo incrível hoje?</p>
                </div>
                <div class="quick-action">
                    <a href="/gerador" class="btn-primary">
                        <i class="fas fa-plus"></i>
                        Criar Novo Story
                    </a>
                </div>
            </div>

            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stat-content">
                        <h3><%= user.stats.storiesGenerated %></h3>
                        <p>Stories Gerados</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-fire"></i>
                    </div>
                    <div class="stat-content">
                        <h3><%= user.stats.consecutiveDays %></h3>
                        <p>Dias Consecutivos</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                    <div class="stat-content">
                        <h3><%= user.stats.favoritesCount %></h3>
                        <p>Favoritos</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-bullhorn"></i>
                    </div>
                    <div class="stat-content">
                        <h3><%= user.stats.ctasUsed %>/50</h3>
                        <p>CTAs Testados</p>
                    </div>
                </div>
            </div>

            <!-- Weekly Challenge -->
            <div class="challenge-card">
                <div class="challenge-header">
                    <h2>🎯 Desafio da Semana</h2>
                    <div class="challenge-badge">
                        <%= Math.round((weeklyChallenge.progress / weeklyChallenge.target) * 100) %>% concluído
                    </div>
                </div>                <div class="challenge-content">
                    <p><%= weeklyChallenge.current %></p>
                    <div class="progress-bar">
                        <div class="progress-fill" data-progress="<%= Math.round((weeklyChallenge.progress / weeklyChallenge.target) * 100) %>"></div>
                    </div>
                    <div class="progress-text">
                        <%= weeklyChallenge.progress %> de <%= weeklyChallenge.target %> concluídos
                    </div>
                    <% if (weeklyChallenge.completed) { %>
                        <div class="challenge-completed">
                            🏆 Parabéns! Sua consistência é incrível!
                        </div>
                    <% } else { %>
                        <div class="challenge-motivation">
                            Você está no caminho certo! Continue assim! 💪
                        </div>
                    <% } %>
                </div>
            </div>

            <!-- Weekly Calendar -->
            <div class="calendar-widget">
                <div class="widget-header">
                    <h2>📅 Calendário Criativo da Semana</h2>
                    <button class="btn-secondary" onclick="expandCalendar()">Ver Mais Dicas</button>
                </div>
                <div class="calendar-grid" id="calendarGrid">
                    <% Object.entries(calendar).forEach(([day, data]) => { %>
                        <div class="calendar-day" onclick="showDayDetails('<%= day %>')">
                            <div class="day-name"><%= day.charAt(0).toUpperCase() + day.slice(1) %></div>
                            <div class="day-theme"><%= data.theme %></div>
                        </div>
                    <% }) %>
                </div>
                <div class="calendar-details" id="calendarDetails" style="display: none;">
                    <!-- Detalhes expandidos serão mostrados aqui -->
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="recent-activity">
                <div class="widget-header">
                    <h2>📋 Atividade Recente</h2>
                    <a href="/historico" class="btn-link">Ver Tudo</a>
                </div>
                <div class="activity-list">
                    <% if (recentHistory && recentHistory.length > 0) { %>
                        <% recentHistory.forEach(item => { %>
                            <div class="activity-item">
                                <div class="activity-icon">
                                    <i class="fas fa-file-alt"></i>
                                </div>                                <div class="activity-content">
                                    <div class="activity-title"><%= item.topic %></div>
                                    <div class="activity-meta">
                                        <span><%= item.createdAt %></span>
                                        <span class="activity-tone"><%= item.tone || 'Casual' %></span>
                                    </div>
                                </div>
                                <div class="activity-actions">
                                    <button onclick="toggleFavorite('<%= item.id %>')" class="btn-icon">
                                        <i class="<%= item.isFavorite ? 'fas' : 'far' %> fa-heart"></i>
                                    </button>
                                </div>
                            </div>
                        <% }) %>
                    <% } else { %>
                        <div class="empty-state">
                            <i class="fas fa-inbox"></i>
                            <p>Nenhuma atividade recente</p>
                            <a href="/gerador" class="btn-primary">Gerar Primeiro Story</a>
                        </div>
                    <% } %>
                </div>
            </div>
        </main>
    </div>

    <script src="/js/dashboard.js"></script>
</body>
</html>
