const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const moment = require('moment');
require('dotenv').config();

// Configurações do ambiente
const config = {
  port: process.env.PORT || 3000,
  github: {
    token: process.env.GITHUB_TOKEN || null,
    endpoint: process.env.GITHUB_ENDPOINT || 'https://models.inference.ai.github.com',
    model: process.env.AI_MODEL || 'gpt-4o',
    maxTokens: parseInt(process.env.AI_MAX_TOKENS) || 500,
    temperature: parseFloat(process.env.AI_TEMPERATURE) || 0.8
  },
  rateLimit: {
    window: parseInt(process.env.RATE_LIMIT_WINDOW) || 60000,
    maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 10
  },
  cache: {
    maxSize: parseInt(process.env.CACHE_MAX_SIZE) || 100
  },
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    apiRequests: process.env.LOG_API_REQUESTS === 'true'
  },
  security: {
    corsOrigin: process.env.CORS_ORIGIN || '*',
    trustProxy: process.env.TRUST_PROXY === 'true'
  }
};

// Rate limiting
const rateLimitMap = new Map();

const app = express();

// Configurar trust proxy se necessário
if (config.security.trustProxy) {
  app.set('trust proxy', true);
}

// Middleware de rate limiting
function rateLimitMiddleware(req, res, next) {
  const clientIP = req.ip || req.connection.remoteAddress || 'unknown';
  const now = Date.now();

  if (!rateLimitMap.has(clientIP)) {
    rateLimitMap.set(clientIP, { count: 1, resetTime: now + config.rateLimit.window });
    return next();
  }

  const clientData = rateLimitMap.get(clientIP);

  if (now > clientData.resetTime) {
    // Reset do contador
    rateLimitMap.set(clientIP, { count: 1, resetTime: now + config.rateLimit.window });
    return next();
  }

  if (clientData.count >= config.rateLimit.maxRequests) {
    return res.status(429).json({
      success: false,
      message: 'Muitas solicitações. Tente novamente em 1 minuto.',
      errorCode: 'RATE_LIMIT_EXCEEDED'
    });
  }

  clientData.count++;
  next();
}

// Configurações do servidor
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));
app.use(express.static('public'));
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// Middleware de logging
function loggingMiddleware(req, res, next) {
  const start = Date.now();
  const originalSend = res.send;

  res.send = function(data) {
    const duration = Date.now() - start;
    const logData = {
      method: req.method,
      url: req.url,
      status: res.statusCode,
      duration: `${duration}ms`,
      timestamp: new Date().toISOString(),
      userAgent: req.get('User-Agent') || 'unknown'
    };

    if (req.url.startsWith('/api/')) {
      console.log('📊 API Request:', logData);
    }

    originalSend.call(this, data);
  };

  next();
}

// Aplicar middlewares
app.use(loggingMiddleware);
app.use('/api/', rateLimitMiddleware);

// Sistema de armazenamento em arquivo
const DATA_FILE = path.join(__dirname, 'data.json');

// Configuração da IA do GitHub
const GITHUB_API_KEY = process.env.GITHUB_TOKEN || null;
const GITHUB_MODELS_ENDPOINT = 'https://models.inference.ai.github.com';

// Função para carregar dados
function loadData() {
    try {
        if (fs.existsSync(DATA_FILE)) {
            const data = fs.readFileSync(DATA_FILE, 'utf8');
            return JSON.parse(data);
        }
    } catch (error) {
        console.error('Erro ao carregar dados:', error);
    }
    
    // Dados padrão se arquivo não existir
    return {
        user: {
            id: 'user123',
            name: 'Usuário',
            niche: 'fitness',
            plan: 'monthly',
            createdAt: moment().format(),
            stats: {
                storiesGenerated: 0,
                consecutiveDays: 1,
                ctasUsed: 0,
                favoritesCount: 0
            }
        },
        history: [],
        favorites: [],
        weeklyChallenge: {
            current: 'Gere 5 stories esta semana',
            progress: 0,
            target: 5,
            completed: false
        }
    };
}

// Função para salvar dados com backup
function saveData(data) {
    try {
        // Criar backup do arquivo atual antes de sobrescrever
        if (fs.existsSync(DATA_FILE)) {
            const backupFile = DATA_FILE + '.backup';
            fs.copyFileSync(DATA_FILE, backupFile);
        }

        // Salvar novos dados
        fs.writeFileSync(DATA_FILE, JSON.stringify(data, null, 2));
        return true;
    } catch (error) {
        console.error('❌ Erro ao salvar dados:', error);

        // Tentar restaurar backup se houver erro
        const backupFile = DATA_FILE + '.backup';
        if (fs.existsSync(backupFile)) {
            try {
                fs.copyFileSync(backupFile, DATA_FILE);
                console.log('🔄 Backup restaurado após erro de salvamento');
            } catch (restoreError) {
                console.error('❌ Erro ao restaurar backup:', restoreError);
            }
        }

        return false;
    }
}

// Sistema de IA do GitHub para geração de stories
class GitHubAI {
    constructor() {
        this.model = 'gpt-4o'; // ou 'gpt-3.5-turbo', 'claude-3-sonnet', etc.
        this.maxTokens = 500;
        this.temperature = 0.8;
        this.cache = new Map(); // Cache simples para templates
        this.cacheMaxSize = 100;
        this.cacheHits = 0;
        this.cacheMisses = 0;
    }

    async generateStory(topic, tone, niche) {
        try {
            console.log(`🤖 GitHub AI - Gerando story para: "${topic}", Tom: "${tone}", Nicho: "${niche}"`);

            // Verificar cache primeiro
            const cacheKey = `${topic.toLowerCase()}-${tone.toLowerCase()}-${niche.toLowerCase()}`;
            if (this.cache.has(cacheKey)) {
                this.cacheHits++;
                console.log(`💾 Cache hit! (${this.cacheHits}/${this.cacheHits + this.cacheMisses})`);
                return this.cache.get(cacheKey);
            }

            this.cacheMisses++;

            // Criar prompt especializado
            const prompt = this.createPrompt(topic, tone, niche);
            
            // Tentar usar GitHub Models API
            if (GITHUB_API_KEY) {
                try {
                    const aiResponse = await this.callGitHubModels(prompt);
                    if (aiResponse) {
                        console.log('✅ Story gerado via GitHub Models API');
                        return aiResponse;
                    }
                } catch (error) {
                    console.warn('⚠️ Falha na API do GitHub, usando fallback:', error.message);
                }
            } else {
                console.log('ℹ️ GitHub API Key não configurada, usando geração local');
            }
            
            // Fallback para templates locais melhorados
            console.log('🔄 Usando geração local com templates avançados');
            const result = this.generateAdvancedLocalStory(topic, tone, niche);

            // Adicionar ao cache
            this.addToCache(cacheKey, result);

            return result;

        } catch (error) {
            console.error('❌ Erro na geração do story:', error);
            const fallbackResult = this.generateBasicFallback(topic, niche);

            // Adicionar fallback ao cache também
            this.addToCache(cacheKey, fallbackResult);

            return fallbackResult;
        }
    }

    addToCache(key, value) {
        // Limpar cache se estiver muito grande
        if (this.cache.size >= this.cacheMaxSize) {
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
        }

        this.cache.set(key, value);
        console.log(`💾 Adicionado ao cache: ${key} (${this.cache.size}/${this.cacheMaxSize})`);
    }

    createPrompt(topic, tone, niche) {
        const toneInstructions = {
            casual: "Use linguagem descontraída, emojis, seja pessoal e compartilhe experiências. Tom amigável e próximo.",
            professional: "Use linguagem técnica, dados, seja objetivo e apresente informações baseadas em evidências. Tom autoridade.",
            sales: "Use linguagem persuasiva, crie urgência, destaque benefícios e inclua call-to-action forte. Tom vendedor."
        };

        const nicheContext = {
            fitness: "Foque em treinos, saúde, motivação, resultados físicos, bem-estar e transformação corporal.",
            beleza: "Foque em skincare, maquiagem, cuidados pessoais, autoestima, produtos de beleza e rotinas.",
            negocios: "Foque em empreendedorismo, vendas, marketing, liderança, produtividade e crescimento empresarial.",
            lifestyle: "Foque em qualidade de vida, equilíbrio, bem-estar mental, hábitos saudáveis e desenvolvimento pessoal."
        };

        return `Crie um story para Instagram sobre "${topic}" para o nicho de ${niche}.

Tom: ${tone.toLowerCase()} - ${toneInstructions[tone.toLowerCase()] || toneInstructions.casual}

Contexto do nicho: ${nicheContext[niche.toLowerCase()] || nicheContext.lifestyle}

Requisitos:
- Máximo 280 caracteres por slide (formato story Instagram)
- Use emojis relevantes
- Inclua hashtags apropriadas (3-5)
- Seja autêntico e engajador
- Estruture em párrafos curtos
- Termine com pergunta ou call-to-action

Gere um story único, original e que desperte interesse genuíno do público.`;
    }

    async callGitHubModels(prompt) {
        try {
            console.log('🔄 Tentando conectar com GitHub Models API...');

            const response = await fetch(`${GITHUB_MODELS_ENDPOINT}/chat/completions`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${GITHUB_API_KEY}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    model: this.model,
                    messages: [
                        {
                            role: 'system',
                            content: 'Você é um especialista em criação de conteúdo para Instagram Stories, focado em gerar textos engajadores e autênticos.'
                        },
                        {
                            role: 'user',
                            content: prompt
                        }
                    ],
                    max_tokens: this.maxTokens,
                    temperature: this.temperature
                })
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`GitHub API Error ${response.status}: ${errorText}`);
            }

            const data = await response.json();

            if (!data.choices || !data.choices[0] || !data.choices[0].message) {
                throw new Error('Resposta inválida da API do GitHub');
            }

            return data.choices[0].message.content.trim();

        } catch (error) {
            console.error('❌ GitHub Models API Error:', error.message);

            // Log mais detalhado para debugging
            if (error.message.includes('401')) {
                console.error('🔑 Token de autenticação inválido ou expirado');
            } else if (error.message.includes('403')) {
                console.error('🚫 Acesso negado - verifique permissões do token');
            } else if (error.message.includes('429')) {
                console.error('⏰ Rate limit excedido - tente novamente mais tarde');
            }

            return null;
        }
    }

    generateAdvancedLocalStory(topic, tone, niche) {
        console.log('🎯 Gerando story local avançado...');
        
        // Templates mais sofisticados e naturais
        const advancedTemplates = {
            casual: {
                fitness: [
                    `Gente, acabei de descobrir algo sobre ${topic} que tá me deixando empolgado(a)! 🤯\n\nSabe aquela sensação de "por que ninguém me contou isso antes"? Então... é exatamente isso que tô sentindo!\n\nOs resultados que consegui só nas primeiras semanas:\n✨ Energia lá em cima\n✨ Performance no treino absurda\n✨ Recuperação muito mais rápida\n\nVocês já experimentaram? Me contem tudo! 💪\n\n#fitness #descoberta #energia`,
                    
                    `Story time sobre ${topic}! 📖\n\nLembra quando eu falei que estava meio desanimado(a) com os treinos? Então...\n\nResolvi testar essa abordagem e cara, que diferença!\n\n🔥 Voltou a paixão pelo treino\n🔥 Resultados mais consistentes\n🔥 Zero monotonia\n\nQuem mais passou por essa fase? Como saíram dela? 💬\n\n#motivacao #treino #transformacao`,
                    
                    `Confesso que estava cético(a) sobre ${topic} 🤔\n\nMas depois de 30 dias testando... gente, preciso admitir que estava errado(a)!\n\nO que mais me impressionou:\n💯 Simplicidade da execução\n💯 Resultados visíveis rapidinho\n💯 Se encaixa na rotina corrida\n\nAlguém mais era resistente no começo? 😅\n\n#mudancadementalidade #fitness #resultados`
                ],
                
                beleza: [
                    `Meninas, preciso dividir essa descoberta sobre ${topic}! ✨\n\nAcabei de ter uma daquelas experiências que mudam tudo... sabe?\n\nMinha pele nunca esteve assim:\n🌟 Brilho natural incrível\n🌟 Textura lisinha\n🌟 Autoestima nas alturas\n\nVocês sentem essa diferença quando acertam na rotina? 💕\n\n#skincare #autoestima #cuidadospessoais`,
                    
                    `Gente, ${topic} entrou na minha vida e revolucionou tudo! 💄\n\nAntes eu achava que era só mais uma tendência, mas depois de testar...\n\nOs comentários que mais recebo:\n🦋 "Sua pele tá radiante!"\n🦋 "Que produto você usa?"\n🦋 "Você rejuvenesceu!"\n\nQuem mais ama receber esses elogios? 😍\n\n#beleza #radiance #confianca`,
                    
                    `Vou contar um segredo sobre ${topic} 🤫\n\nEssa descoberta mudou completamente minha relação com o autocuidado.\n\nAgora minha rotina é:\n✨ Mais prática e rápida\n✨ Resultados duradouros\n✨ Custo-benefício perfeito\n\nQuem mais prioriza praticidade sem abrir mão dos resultados? 🙋‍♀️\n\n#praticidade #autocuidado #results`
                ],
                
                negocios: [
                    `Pessoal, ${topic} mudou completamente meu jogo nos negócios! 🎯\n\nEssa estratégia que implementei há 3 meses já trouxe resultados que nem esperava...\n\nNumeros reais:\n📈 Vendas +40% no trimestre\n📈 Clientes mais engajados\n📈 Processo muito mais fluido\n\nQuem aqui também testa estratégias novas constantemente? 💼\n\n#empreendedorismo #resultados #estrategia`,
                    
                    `Vou ser transparente sobre ${topic} 💬\n\nNo começo, achei que era só mais uma "modinha" do mercado...\n\nMas os números não mentem:\n🚀 ROI impressionante\n🚀 Eficiência operacional dobrou\n🚀 Satisfação da equipe aumentou\n\nAlguém mais já se surpreendeu assim com uma estratégia? 📊\n\n#transparencia #negocios #growth`,
                    
                    `Reflexão sobre ${topic} no meu negócio 🤔\n\nImplementei essa abordagem meio receoso, mas decidido a testar...\n\nResultado após 2 meses:\n💡 Processos otimizados\n💡 Time mais produtivo\n💡 Margem de lucro melhor\n\nQuem mais gosta de testar coisas novas mesmo com receio? 💪\n\n#inovacao #coragem #empresarial`
                ],
                
                lifestyle: [
                    `${topic} tem sido um divisor de águas na minha vida! 🌟\n\nEssa mudança de perspectiva trouxe uma leveza que eu não sentia há tempos...\n\nO que mais mudou:\n✨ Ansiedade diminuiu muito\n✨ Relacionamentos mais leves\n✨ Propósito mais claro\n\nVocês também sentem quando algo "clica" na vida? 💫\n\n#bemestar #equilibrio #proposito`,
                    
                    `Hoje quero falar sobre ${topic} de coração aberto 💕\n\nEssa prática entrou na minha rotina e trouxe uma paz interior incrível.\n\nBenefícios que sinto:\n🌸 Dias mais leves\n🌸 Gratidão constante\n🌸 Energia renovada\n\nQuem mais busca essa harmonia no dia a dia? 🧘‍♀️\n\n#mindfulness #gratidao #vida`,
                    
                    `Confissão: ${topic} me ensinou muito sobre mim mesmo(a) 🌱\n\nEssa jornada de autoconhecimento tem sido transformadora...\n\nAprendizados principais:\n🦋 Autocompaixão é fundamental\n🦋 Pequenos hábitos fazem diferença\n🦋 O processo é mais importante que o resultado\n\nQuem mais está nessa jornada de crescimento? ✨\n\n#autoconhecimento #crescimento #jornada`
                ]
            },
            
            professional: {
                fitness: [
                    `Análise técnica: ${topic} 📊\n\nBaseado em estudos recentes (meta-análise 2024), os dados mostram:\n\n• Melhora de 35% na capacidade aeróbica\n• Redução de 28% em marcadores inflamatórios\n• Otimização da composição corporal\n\nProtocolo recomendado para atletas intermediários.\n\n#sciencebased #performance #data`,
                    
                    `Evidências científicas sobre ${topic} 🔬\n\nRevisão sistemática demonstra eficácia:\n\n✓ Validação em 12 estudos clínicos\n✓ Amostra: 2.847 participantes\n✓ Significância estatística: p<0.001\n\nImplementação recomendada com supervisão profissional.\n\n#evidenciascientificas #research #methodology`,
                    
                    `Protocolo profissional: ${topic} 💪\n\nAnálise baseada em biomecânica avançada:\n\n📈 Eficiência muscular: +42%\n📈 Economia de movimento: +28%\n📈 Prevenção de lesões: +65%\n\nIdeal para otimização de performance atlética.\n\n#biomechanics #optimization #professional`
                ],
                
                beleza: [
                    `Análise dermatológica: ${topic} ✨\n\nEstudos clínicos comprovam:\n\n• Melhora da hidratação em 48%\n• Redução de linhas finas em 32%\n• Uniformização do tom em 89% dos casos\n\nFormulação aprovada pela ANVISA.\n\n#dermatologia #clinicalproof #innovation`,
                    
                    `Avaliação científica de ${topic} 🔬\n\nTestes laboratoriais confirmam:\n\n✓ Ativo principal: concentração ótima\n✓ Estabilidade: 24 meses testada\n✓ Biocompatibilidade: 99.7%\n\nClassificação: Cosmético de grau médico.\n\n#cosmetologia #qualidade #safety`,
                    
                    `Parecer técnico sobre ${topic} 💄\n\nAnálise química detalhada:\n\n📊 pH balanceado: 5.5\n📊 Penetração cutânea: otimizada\n📊 Eficácia comprovada: 96% satisfação\n\nRecomendado por dermatologistas.\n\n#chemistry #skincare #professional`
                ],
                
                negocios: [
                    `Análise estratégica: ${topic} 📈\n\nCase study empresarial demonstra:\n\n• ROI de 287% em 12 meses\n• CAGR (crescimento) de 45% ao ano\n• Redução de CAC em 35%\n\nMetodologia aplicável a scale-ups B2B.\n\n#strategy #roi #scalability`,
                    
                    `KPIs principais após implementar ${topic} 💼\n\nResultados quantitativos (Q1-Q2):\n\n✓ Revenue: +52%\n✓ Conversão: 18% → 29%\n✓ LTV/CAC ratio: 4.2x\n\nFramework replicável para SaaS.\n\n#kpis #saas #growth`,
                    
                    `Benchmarking: ${topic} no mercado 📊\n\nComparação com top players:\n\n🎯 Eficiência operacional: 23% acima da média\n🎯 Market share: crescimento de 12%\n🎯 NPS: 78 (excelente)\n\nPosicionamento competitivo otimizado.\n\n#benchmarking #competitive #analysis`
                ]
            },
            
            sales: [
                `🔥 OPORTUNIDADE ÚNICA sobre ${topic}!\n\nMais de 10.000 pessoas já transformaram suas vidas com essa descoberta!\n\n⚡ Resultados em apenas 21 dias\n⚡ Método 100% comprovado\n⚡ Suporte completo incluso\n\n🚨 DESCONTO de 50% nas próximas 12h!\n👆 DM para garantir - últimas vagas!\n\n#oportunidade #transformacao #limitado`,
                
                `🚨 ATENÇÃO: ${topic} pode ser sua virada de chave!\n\nEsse sistema já mudou a vida de milhares!\n\n✨ Metodologia exclusiva\n✨ Resultados garantidos\n✨ Acesso vitalício\n\n💥 BÔNUS especial por tempo limitado!\n📲 Link na bio - não perca!\n\n#viradamesa #exclusivo #bonus`,
                
                `💎 REVELAÇÃO sobre ${topic} que poucos sabem!\n\nSegredo usado pelos maiores especialistas:\n\n🎯 Técnica revolucionária\n🎯 Aplicação imediata\n🎯 Resultados exponenciais\n\n🔥 MASTERCLASS gratuita hoje às 20h!\n👆 Stories para se inscrever!\n\n#revelacao #masterclass #gratuito`
            ]
        };

        // Selecionar template baseado no tom e nicho
        const toneTemplates = advancedTemplates[tone.toLowerCase()];
        let templates;
        
        if (toneTemplates && toneTemplates[niche.toLowerCase()]) {
            templates = toneTemplates[niche.toLowerCase()];
        } else if (toneTemplates && Array.isArray(toneTemplates)) {
            templates = toneTemplates;
        } else {
            // Fallback para templates genéricos
            templates = this.getGenericTemplates(topic, niche);
        }

        // Selecionar template aleatório
        const selectedTemplate = templates[Math.floor(Math.random() * templates.length)];
        
        console.log('✅ Story local avançado gerado com sucesso!');
        return selectedTemplate;
    }

    getGenericTemplates(topic, niche) {
        return [
            `Descoberta incrível sobre ${topic}! ✨\n\nEssa mudança trouxe resultados que nem esperava:\n\n🌟 Impacto positivo imediato\n🌟 Facilidade de implementação\n🌟 Resultados sustentáveis\n\nQuem mais quer saber os detalhes? Me chamem! 💬\n\n${this.getHashtagsForNiche(niche).slice(0, 3).join(' ')}`,

            `${topic} entrou na minha vida e mudou tudo! 🔥\n\nDepois de muito testar, finalmente encontrei algo que funciona:\n\n💪 Resultados consistentes\n💪 Processo simplificado\n💪 Satisfação garantida\n\nVocês têm curiosidade sobre como implementar? 🚀\n\n${this.getHashtagsForNiche(niche).slice(0, 3).join(' ')}`
        ];
    }

    getHashtagsForNiche(niche) {
        const hashtags = {
            fitness: ['#fitness', '#treino', '#saude', '#motivacao', '#academia', '#vidasaudavel', '#wellness'],
            beleza: ['#beleza', '#skincare', '#makeup', '#autocuidado', '#selfcare', '#beauty', '#glow'],
            negocios: ['#empreendedorismo', '#negocios', '#sucesso', '#vendas', '#marketing', '#business', '#growth'],
            lifestyle: ['#lifestyle', '#bemestar', '#equilibrio', '#mindfulness', '#qualidadedevida', '#inspiracao', '#vida']
        };
        
        return hashtags[niche.toLowerCase()] || hashtags.lifestyle;
    }

    generateBasicFallback(topic, niche) {
        console.log('🔄 Usando fallback básico...');
        return `Hoje quero compartilhar sobre ${topic}! ✨\n\nEssa descoberta tem feito diferença real na minha jornada.\n\nPrincipais benefícios:\n• Resultados práticos\n• Fácil implementação\n• Impacto positivo\n\nQuem mais tem interesse nesse assunto? 💬\n\n${this.getHashtagsForNiche(niche).slice(0, 3).join(' ')}`;
    }
}

// A classe StoryAI foi removida - agora usamos apenas GitHubAI com fallback integrado

// Carregar dados iniciais
let userData = loadData();

// Instanciar o gerador de IA principal (com fallback para GitHub API)
const storyAI = new GitHubAI();

// Salvar dados apenas quando há mudanças (otimização)
let dataChanged = false;
const saveDataIfChanged = () => {
    if (dataChanged) {
        const success = saveData(userData);
        if (success) {
            dataChanged = false;
            console.log('💾 Dados salvos automaticamente');
        }
    }
};

// Verificar mudanças a cada 30 segundos (otimizado)
setInterval(saveDataIfChanged, 30000);

// Base de dados para funcionalidades extras
const ctas = {
  vendas: [
    'Não perca esta oportunidade única!',
    'Últimas vagas disponíveis!',
    'Oferta válida por tempo limitado!',
    'Clique no link da bio e garante o seu!'
  ],
  engajamento: [
    'Comenta aqui embaixo o que você achou!',
    'Marca aquele amigo que precisa ver isso!',
    'Salva este post para não esquecer!',
    'Qual sua opinião sobre isso?'
  ],
  trafego: [
    'Link na bio para saber mais!',
    'Acesse nosso site e confira!',
    'Desliza para ver mais conteúdo!',
    'Toque no link dos destaques!'
  ]
};

const hashtags = {
  fitness: ['#fitness', '#treino', '#saude', '#motivacao', '#academia'],
  beleza: ['#beleza', '#skincare', '#makeup', '#autocuidado', '#dicas'],
  negocios: ['#empreendedorismo', '#negocios', '#sucesso', '#vendas', '#marketing'],
  lifestyle: ['#lifestyle', '#dicas', '#inspiracao', '#vida', '#bemestar']
};

const mentalTriggers = {
  escassez: [
    'Apenas hoje!',
    'Últimas unidades!',
    'Vagas limitadas!',
    'Por tempo limitado!'
  ],
  urgencia: [
    'Não deixe para depois!',
    'Agora ou nunca!',
    'Tempo acabando!',
    'Últimas horas!'
  ],
  reciprocidade: [
    'Especialmente para você!',
    'Um presente de agradecimento!',
    'Retribuindo seu carinho!',
    'Como forma de gratidão!'
  ]
};

const weeklyCalendar = {
  segunda: { theme: 'Motivação Monday', tip: 'Comece a semana inspirando seus seguidores' },
  terca: { theme: 'Tutorial Tuesday', tip: 'Ensine algo novo e útil' },
  quarta: { theme: 'Wisdom Wednesday', tip: 'Compartilhe conhecimento e experiências' },
  quinta: { theme: 'Throwback Thursday', tip: 'Conte uma história do passado' },
  sexta: { theme: 'Feel Good Friday', tip: 'Celebre conquistas e momentos positivos' },
  sabado: { theme: 'Saturday Stories', tip: 'Seja mais descontraído e pessoal' },
  domingo: { theme: 'Sunday Reflection', tip: 'Reflita sobre a semana e planeje a próxima' }
};

const reelsIdeas = [
  'Antes e depois transformação',
  'Dica rápida em 15 segundos',
  'Erro comum que você deve evitar',
  'Minha rotina matinal',
  'Produto que mudou minha vida',
  '3 segredos que ninguém conta',
  'Pergunta e resposta com seguidores',
  'Bastidores do meu trabalho',
  'Comparação: expectativa vs realidade',
  'Tutorial passo a passo'
];

// Rotas principais
app.get('/', (_req, res) => {
  res.render('dashboard', {
    user: userData.user,
    weeklyChallenge: userData.weeklyChallenge,
    calendar: weeklyCalendar,
    recentHistory: userData.history.slice(-5)
  });
});

app.get('/ferramentas-extras', (_req, res) => {
  res.render('extras', {
    ctas,
    hashtags,
    mentalTriggers,
    reelsIdeas,
    user: userData.user
  });
});

app.get('/historico', (_req, res) => {
  res.render('history', {
    history: userData.history,
    favorites: userData.favorites,
    user: userData.user
  });
});

app.get('/gerador', (_req, res) => {
  res.render('generator', { user: userData.user });
});

// Função de validação e sanitização
function validateAndSanitizeInput(topic, tone, niche) {
  const errors = [];

  // Validar topic
  if (!topic || typeof topic !== 'string') {
    errors.push('Tópico é obrigatório e deve ser texto');
  } else if (topic.trim().length < 3) {
    errors.push('Tópico deve ter pelo menos 3 caracteres');
  } else if (topic.trim().length > 100) {
    errors.push('Tópico deve ter no máximo 100 caracteres');
  }

  // Validar tone
  const validTones = ['casual', 'professional', 'sales'];
  if (!tone || !validTones.includes(tone.toLowerCase())) {
    errors.push('Tom deve ser: casual, professional ou sales');
  }

  // Validar niche
  const validNiches = ['fitness', 'beleza', 'negocios', 'lifestyle'];
  if (!niche || !validNiches.includes(niche.toLowerCase())) {
    errors.push('Nicho deve ser: fitness, beleza, negocios ou lifestyle');
  }

  return {
    isValid: errors.length === 0,
    errors,
    sanitized: {
      topic: topic ? topic.trim().substring(0, 100) : '',
      tone: tone ? tone.toLowerCase().trim() : '',
      niche: niche ? niche.toLowerCase().trim() : ''
    }
  };
}

// API endpoints
app.post('/api/generate-story', (req, res) => {
  const { topic, tone, niche } = req.body;

  console.log('📝 Recebida solicitação de geração:', { topic, tone, niche });

  // Validar e sanitizar entrada
  const validation = validateAndSanitizeInput(topic, tone, niche);
  if (!validation.isValid) {
    console.log('❌ Dados inválidos recebidos:', validation.errors);
    return res.status(400).json({
      success: false,
      message: 'Dados inválidos: ' + validation.errors.join(', ')
    });
  }

  const { topic: cleanTopic, tone: cleanTone, niche: cleanNiche } = validation.sanitized;

  try {
    // Gerar conteúdo usando IA
    console.log('🤖 Iniciando geração com IA...');
    const generatedContent = storyAI.generateStory(cleanTopic, cleanTone, cleanNiche);
    console.log('✅ Conteúdo gerado:', generatedContent.substring(0, 100) + '...');

    const story = {
      id: uuidv4(),
      topic: cleanTopic,
      tone: cleanTone,
      niche: cleanNiche,
      content: generatedContent,
      createdAt: moment().format('DD/MM/YYYY HH:mm'),
      isFavorite: false
    };
    
    // Adicionar ao histórico
    userData.history.unshift(story);
    userData.user.stats.storiesGenerated++;

    // Atualizar desafio semanal
    if (userData.weeklyChallenge.progress < userData.weeklyChallenge.target) {
      userData.weeklyChallenge.progress++;
      if (userData.weeklyChallenge.progress === userData.weeklyChallenge.target) {
        userData.weeklyChallenge.completed = true;
      }
    }

    // Marcar dados como alterados para salvamento automático
    dataChanged = true;
    
    console.log('🎉 Story gerado e salvo com sucesso!');
    res.json({ success: true, story });
    
  } catch (error) {
    console.error('❌ Erro ao gerar story:', {
      error: error.message,
      stack: error.stack,
      input: { topic: cleanTopic, tone: cleanTone, niche: cleanNiche }
    });

    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor ao gerar story. Tente novamente em alguns instantes.',
      errorCode: 'GENERATION_ERROR'
    });
  }
});

app.post('/api/toggle-favorite/:id', (req, res) => {
  const storyId = req.params.id;
  const story = userData.history.find(s => s.id === storyId);
  
  if (story) {
    story.isFavorite = !story.isFavorite;
    
    if (story.isFavorite) {
      // Verificar se já não está nos favoritos
      const existingFav = userData.favorites.find(f => f.id === storyId);
      if (!existingFav) {
        userData.favorites.push(story);
        userData.user.stats.favoritesCount++;
      }
    } else {
      userData.favorites = userData.favorites.filter(f => f.id !== storyId);
      userData.user.stats.favoritesCount = Math.max(0, userData.user.stats.favoritesCount - 1);
    }
    
    // Marcar dados como alterados para salvamento automático
    dataChanged = true;
    
    res.json({ success: true, isFavorite: story.isFavorite });
  } else {
    res.status(404).json({ success: false, message: 'Story não encontrado' });
  }
});

app.get('/api/hashtags/:niche', (req, res) => {
  const niche = req.params.niche.toLowerCase();
  const tags = hashtags[niche] || hashtags.lifestyle;
  res.json({ hashtags: tags });
});

app.get('/api/user-stats', (_req, res) => {
  res.json({ stats: userData.user.stats, challenge: userData.weeklyChallenge });
});

app.get('/api/system-stats', (_req, res) => {
  const cacheStats = {
    size: storyAI.cache.size,
    maxSize: storyAI.cacheMaxSize,
    hits: storyAI.cacheHits,
    misses: storyAI.cacheMisses,
    hitRate: storyAI.cacheHits + storyAI.cacheMisses > 0
      ? ((storyAI.cacheHits / (storyAI.cacheHits + storyAI.cacheMisses)) * 100).toFixed(2) + '%'
      : '0%'
  };

  const systemStats = {
    uptime: process.uptime(),
    memoryUsage: process.memoryUsage(),
    nodeVersion: process.version,
    platform: process.platform
  };

  res.json({
    cache: cacheStats,
    system: systemStats,
    data: {
      totalStories: userData.history.length,
      totalFavorites: userData.favorites.length
    }
  });
});

// Iniciar servidor
app.listen(PORT, () => {
  console.log(`🚀 Servidor rodando na porta ${PORT}`);
  console.log(`🌐 Acesse: http://localhost:${PORT}`);
  console.log(`🤖 IA de geração de stories carregada com sucesso!`);
  console.log(`📊 Dados de usuário carregados:`, userData.user.stats);
});
