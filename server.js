const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const moment = require('moment');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Configurações do servidor
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));
app.use(express.static('public'));
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// Sistema de armazenamento em arquivo
const DATA_FILE = path.join(__dirname, 'data.json');

// Configuração da IA do GitHub
const GITHUB_API_KEY = process.env.GITHUB_TOKEN || 'your_github_token_here';
const GITHUB_MODELS_ENDPOINT = 'https://models.inference.ai.azure.com';

// Função para carregar dados
function loadData() {
    try {
        if (fs.existsSync(DATA_FILE)) {
            const data = fs.readFileSync(DATA_FILE, 'utf8');
            return JSON.parse(data);
        }
    } catch (error) {
        console.error('Erro ao carregar dados:', error);
    }
    
    // Dados padrão se arquivo não existir
    return {
        user: {
            id: 'user123',
            name: 'Usuário',
            niche: 'fitness',
            plan: 'monthly',
            createdAt: moment().format(),
            stats: {
                storiesGenerated: 0,
                consecutiveDays: 1,
                ctasUsed: 0,
                favoritesCount: 0
            }
        },
        history: [],
        favorites: [],
        weeklyChallenge: {
            current: 'Gere 5 stories esta semana',
            progress: 0,
            target: 5,
            completed: false
        }
    };
}

// Função para salvar dados
function saveData(data) {
    try {
        fs.writeFileSync(DATA_FILE, JSON.stringify(data, null, 2));
        return true;
    } catch (error) {
        console.error('Erro ao salvar dados:', error);
        return false;
    }
}

// Sistema de IA do GitHub para geração de stories
class GitHubAI {
    constructor() {
        this.model = 'gpt-4o'; // ou 'gpt-3.5-turbo', 'claude-3-sonnet', etc.
        this.maxTokens = 500;
        this.temperature = 0.8;
    }

    async generateStory(topic, tone, niche) {
        try {
            console.log(`🤖 GitHub AI - Gerando story para: "${topic}", Tom: "${tone}", Nicho: "${niche}"`);
            
            // Criar prompt especializado
            const prompt = this.createPrompt(topic, tone, niche);
            
            // Tentar usar GitHub Models API
            if (GITHUB_API_KEY && GITHUB_API_KEY !== 'your_github_token_here') {
                try {
                    const aiResponse = await this.callGitHubModels(prompt);
                    if (aiResponse) {
                        console.log('✅ Story gerado via GitHub Models API');
                        return aiResponse;
                    }
                } catch (error) {
                    console.warn('⚠️ Falha na API do GitHub, usando fallback:', error.message);
                }
            }
            
            // Fallback para templates locais melhorados
            console.log('🔄 Usando geração local com templates avançados');
            return this.generateAdvancedLocalStory(topic, tone, niche);
            
        } catch (error) {
            console.error('❌ Erro na geração do story:', error);
            return this.generateBasicFallback(topic, tone, niche);
        }
    }

    createPrompt(topic, tone, niche) {
        const toneInstructions = {
            casual: "Use linguagem descontraída, emojis, seja pessoal e compartilhe experiências. Tom amigável e próximo.",
            professional: "Use linguagem técnica, dados, seja objetivo e apresente informações baseadas em evidências. Tom autoridade.",
            sales: "Use linguagem persuasiva, crie urgência, destaque benefícios e inclua call-to-action forte. Tom vendedor."
        };

        const nicheContext = {
            fitness: "Foque em treinos, saúde, motivação, resultados físicos, bem-estar e transformação corporal.",
            beleza: "Foque em skincare, maquiagem, cuidados pessoais, autoestima, produtos de beleza e rotinas.",
            negocios: "Foque em empreendedorismo, vendas, marketing, liderança, produtividade e crescimento empresarial.",
            lifestyle: "Foque em qualidade de vida, equilíbrio, bem-estar mental, hábitos saudáveis e desenvolvimento pessoal."
        };

        return `Crie um story para Instagram sobre "${topic}" para o nicho de ${niche}.

Tom: ${tone.toLowerCase()} - ${toneInstructions[tone.toLowerCase()] || toneInstructions.casual}

Contexto do nicho: ${nicheContext[niche.toLowerCase()] || nicheContext.lifestyle}

Requisitos:
- Máximo 280 caracteres por slide (formato story Instagram)
- Use emojis relevantes
- Inclua hashtags apropriadas (3-5)
- Seja autêntico e engajador
- Estruture em párrafos curtos
- Termine com pergunta ou call-to-action

Gere um story único, original e que desperte interesse genuíno do público.`;
    }

    async callGitHubModels(prompt) {
        try {
            // Simulação da chamada para GitHub Models API
            // Em produção, substitua por chamada real
            const response = await fetch(`${GITHUB_MODELS_ENDPOINT}/chat/completions`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${GITHUB_API_KEY}`,
                    'Content-Type': 'application/json',
                    'X-GitHub-Token': GITHUB_API_KEY
                },
                body: JSON.stringify({
                    model: this.model,
                    messages: [
                        {
                            role: 'system',
                            content: 'Você é um especialista em criação de conteúdo para Instagram Stories, focado em gerar textos engajadores e autênticos.'
                        },
                        {
                            role: 'user',
                            content: prompt
                        }
                    ],
                    max_tokens: this.maxTokens,
                    temperature: this.temperature
                })
            });

            if (response.ok) {
                const data = await response.json();
                return data.choices[0].message.content.trim();
            } else {
                throw new Error(`API Error: ${response.status}`);
            }
        } catch (error) {
            console.error('GitHub Models API Error:', error);
            return null;
        }
    }

    generateAdvancedLocalStory(topic, tone, niche) {
        console.log('🎯 Gerando story local avançado...');
        
        // Templates mais sofisticados e naturais
        const advancedTemplates = {
            casual: {
                fitness: [
                    `Gente, acabei de descobrir algo sobre ${topic} que tá me deixando empolgado(a)! 🤯\n\nSabe aquela sensação de "por que ninguém me contou isso antes"? Então... é exatamente isso que tô sentindo!\n\nOs resultados que consegui só nas primeiras semanas:\n✨ Energia lá em cima\n✨ Performance no treino absurda\n✨ Recuperação muito mais rápida\n\nVocês já experimentaram? Me contem tudo! 💪\n\n#fitness #descoberta #energia`,
                    
                    `Story time sobre ${topic}! 📖\n\nLembra quando eu falei que estava meio desanimado(a) com os treinos? Então...\n\nResolvi testar essa abordagem e cara, que diferença!\n\n🔥 Voltou a paixão pelo treino\n🔥 Resultados mais consistentes\n🔥 Zero monotonia\n\nQuem mais passou por essa fase? Como saíram dela? 💬\n\n#motivacao #treino #transformacao`,
                    
                    `Confesso que estava cético(a) sobre ${topic} 🤔\n\nMas depois de 30 dias testando... gente, preciso admitir que estava errado(a)!\n\nO que mais me impressionou:\n💯 Simplicidade da execução\n💯 Resultados visíveis rapidinho\n💯 Se encaixa na rotina corrida\n\nAlguém mais era resistente no começo? 😅\n\n#mudancadementalidade #fitness #resultados`
                ],
                
                beleza: [
                    `Meninas, preciso dividir essa descoberta sobre ${topic}! ✨\n\nAcabei de ter uma daquelas experiências que mudam tudo... sabe?\n\nMinha pele nunca esteve assim:\n🌟 Brilho natural incrível\n🌟 Textura lisinha\n🌟 Autoestima nas alturas\n\nVocês sentem essa diferença quando acertam na rotina? 💕\n\n#skincare #autoestima #cuidadospessoais`,
                    
                    `Gente, ${topic} entrou na minha vida e revolucionou tudo! 💄\n\nAntes eu achava que era só mais uma tendência, mas depois de testar...\n\nOs comentários que mais recebo:\n🦋 "Sua pele tá radiante!"\n🦋 "Que produto você usa?"\n🦋 "Você rejuvenesceu!"\n\nQuem mais ama receber esses elogios? 😍\n\n#beleza #radiance #confianca`,
                    
                    `Vou contar um segredo sobre ${topic} 🤫\n\nEssa descoberta mudou completamente minha relação com o autocuidado.\n\nAgora minha rotina é:\n✨ Mais prática e rápida\n✨ Resultados duradouros\n✨ Custo-benefício perfeito\n\nQuem mais prioriza praticidade sem abrir mão dos resultados? 🙋‍♀️\n\n#praticidade #autocuidado #results`
                ],
                
                negocios: [
                    `Pessoal, ${topic} mudou completamente meu jogo nos negócios! 🎯\n\nEssa estratégia que implementei há 3 meses já trouxe resultados que nem esperava...\n\nNumeros reais:\n📈 Vendas +40% no trimestre\n📈 Clientes mais engajados\n📈 Processo muito mais fluido\n\nQuem aqui também testa estratégias novas constantemente? 💼\n\n#empreendedorismo #resultados #estrategia`,
                    
                    `Vou ser transparente sobre ${topic} 💬\n\nNo começo, achei que era só mais uma "modinha" do mercado...\n\nMas os números não mentem:\n🚀 ROI impressionante\n🚀 Eficiência operacional dobrou\n🚀 Satisfação da equipe aumentou\n\nAlguém mais já se surpreendeu assim com uma estratégia? 📊\n\n#transparencia #negocios #growth`,
                    
                    `Reflexão sobre ${topic} no meu negócio 🤔\n\nImplementei essa abordagem meio receoso, mas decidido a testar...\n\nResultado após 2 meses:\n💡 Processos otimizados\n💡 Time mais produtivo\n💡 Margem de lucro melhor\n\nQuem mais gosta de testar coisas novas mesmo com receio? 💪\n\n#inovacao #coragem #empresarial`
                ],
                
                lifestyle: [
                    `${topic} tem sido um divisor de águas na minha vida! 🌟\n\nEssa mudança de perspectiva trouxe uma leveza que eu não sentia há tempos...\n\nO que mais mudou:\n✨ Ansiedade diminuiu muito\n✨ Relacionamentos mais leves\n✨ Propósito mais claro\n\nVocês também sentem quando algo "clica" na vida? 💫\n\n#bemestar #equilibrio #proposito`,
                    
                    `Hoje quero falar sobre ${topic} de coração aberto 💕\n\nEssa prática entrou na minha rotina e trouxe uma paz interior incrível.\n\nBenefícios que sinto:\n🌸 Dias mais leves\n🌸 Gratidão constante\n🌸 Energia renovada\n\nQuem mais busca essa harmonia no dia a dia? 🧘‍♀️\n\n#mindfulness #gratidao #vida`,
                    
                    `Confissão: ${topic} me ensinou muito sobre mim mesmo(a) 🌱\n\nEssa jornada de autoconhecimento tem sido transformadora...\n\nAprendizados principais:\n🦋 Autocompaixão é fundamental\n🦋 Pequenos hábitos fazem diferença\n🦋 O processo é mais importante que o resultado\n\nQuem mais está nessa jornada de crescimento? ✨\n\n#autoconhecimento #crescimento #jornada`
                ]
            },
            
            professional: {
                fitness: [
                    `Análise técnica: ${topic} 📊\n\nBaseado em estudos recentes (meta-análise 2024), os dados mostram:\n\n• Melhora de 35% na capacidade aeróbica\n• Redução de 28% em marcadores inflamatórios\n• Otimização da composição corporal\n\nProtocolo recomendado para atletas intermediários.\n\n#sciencebased #performance #data`,
                    
                    `Evidências científicas sobre ${topic} 🔬\n\nRevisão sistemática demonstra eficácia:\n\n✓ Validação em 12 estudos clínicos\n✓ Amostra: 2.847 participantes\n✓ Significância estatística: p<0.001\n\nImplementação recomendada com supervisão profissional.\n\n#evidenciascientificas #research #methodology`,
                    
                    `Protocolo profissional: ${topic} 💪\n\nAnálise baseada em biomecânica avançada:\n\n📈 Eficiência muscular: +42%\n📈 Economia de movimento: +28%\n📈 Prevenção de lesões: +65%\n\nIdeal para otimização de performance atlética.\n\n#biomechanics #optimization #professional`
                ],
                
                beleza: [
                    `Análise dermatológica: ${topic} ✨\n\nEstudos clínicos comprovam:\n\n• Melhora da hidratação em 48%\n• Redução de linhas finas em 32%\n• Uniformização do tom em 89% dos casos\n\nFormulação aprovada pela ANVISA.\n\n#dermatologia #clinicalproof #innovation`,
                    
                    `Avaliação científica de ${topic} 🔬\n\nTestes laboratoriais confirmam:\n\n✓ Ativo principal: concentração ótima\n✓ Estabilidade: 24 meses testada\n✓ Biocompatibilidade: 99.7%\n\nClassificação: Cosmético de grau médico.\n\n#cosmetologia #qualidade #safety`,
                    
                    `Parecer técnico sobre ${topic} 💄\n\nAnálise química detalhada:\n\n📊 pH balanceado: 5.5\n📊 Penetração cutânea: otimizada\n📊 Eficácia comprovada: 96% satisfação\n\nRecomendado por dermatologistas.\n\n#chemistry #skincare #professional`
                ],
                
                negocios: [
                    `Análise estratégica: ${topic} 📈\n\nCase study empresarial demonstra:\n\n• ROI de 287% em 12 meses\n• CAGR (crescimento) de 45% ao ano\n• Redução de CAC em 35%\n\nMetodologia aplicável a scale-ups B2B.\n\n#strategy #roi #scalability`,
                    
                    `KPIs principais após implementar ${topic} 💼\n\nResultados quantitativos (Q1-Q2):\n\n✓ Revenue: +52%\n✓ Conversão: 18% → 29%\n✓ LTV/CAC ratio: 4.2x\n\nFramework replicável para SaaS.\n\n#kpis #saas #growth`,
                    
                    `Benchmarking: ${topic} no mercado 📊\n\nComparação com top players:\n\n🎯 Eficiência operacional: 23% acima da média\n🎯 Market share: crescimento de 12%\n🎯 NPS: 78 (excelente)\n\nPosicionamento competitivo otimizado.\n\n#benchmarking #competitive #analysis`
                ]
            },
            
            sales: [
                `🔥 OPORTUNIDADE ÚNICA sobre ${topic}!\n\nMais de 10.000 pessoas já transformaram suas vidas com essa descoberta!\n\n⚡ Resultados em apenas 21 dias\n⚡ Método 100% comprovado\n⚡ Suporte completo incluso\n\n🚨 DESCONTO de 50% nas próximas 12h!\n👆 DM para garantir - últimas vagas!\n\n#oportunidade #transformacao #limitado`,
                
                `🚨 ATENÇÃO: ${topic} pode ser sua virada de chave!\n\nEsse sistema já mudou a vida de milhares!\n\n✨ Metodologia exclusiva\n✨ Resultados garantidos\n✨ Acesso vitalício\n\n💥 BÔNUS especial por tempo limitado!\n📲 Link na bio - não perca!\n\n#viradamesa #exclusivo #bonus`,
                
                `💎 REVELAÇÃO sobre ${topic} que poucos sabem!\n\nSegredo usado pelos maiores especialistas:\n\n🎯 Técnica revolucionária\n🎯 Aplicação imediata\n🎯 Resultados exponenciais\n\n🔥 MASTERCLASS gratuita hoje às 20h!\n👆 Stories para se inscrever!\n\n#revelacao #masterclass #gratuito`
            ]
        };

        // Selecionar template baseado no tom e nicho
        const toneTemplates = advancedTemplates[tone.toLowerCase()];
        let templates;
        
        if (toneTemplates && toneTemplates[niche.toLowerCase()]) {
            templates = toneTemplates[niche.toLowerCase()];
        } else if (toneTemplates && Array.isArray(toneTemplates)) {
            templates = toneTemplates;
        } else {
            // Fallback para templates genéricos
            templates = this.getGenericTemplates(topic, tone, niche);
        }

        // Selecionar template aleatório
        const selectedTemplate = templates[Math.floor(Math.random() * templates.length)];
        
        console.log('✅ Story local avançado gerado com sucesso!');
        return selectedTemplate;
    }

    getGenericTemplates(topic, tone, niche) {
        return [
            `Descoberta incrível sobre ${topic}! ✨\n\nEssa mudança trouxe resultados que nem esperava:\n\n🌟 Impacto positivo imediato\n🌟 Facilidade de implementação\n🌟 Resultados sustentáveis\n\nQuem mais quer saber os detalhes? Me chamem! 💬\n\n${this.getHashtagsForNiche(niche).slice(0, 3).join(' ')}`,
            
            `${topic} entrou na minha vida e mudou tudo! 🔥\n\nDepois de muito testar, finalmente encontrei algo que funciona:\n\n💪 Resultados consistentes\n💪 Processo simplificado\n💪 Satisfação garantida\n\nVocês têm curiosidade sobre como implementar? 🚀\n\n${this.getHashtagsForNiche(niche).slice(0, 3).join(' ')}`
        ];
    }

    getHashtagsForNiche(niche) {
        const hashtags = {
            fitness: ['#fitness', '#treino', '#saude', '#motivacao', '#academia', '#vidasaudavel', '#wellness'],
            beleza: ['#beleza', '#skincare', '#makeup', '#autocuidado', '#selfcare', '#beauty', '#glow'],
            negocios: ['#empreendedorismo', '#negocios', '#sucesso', '#vendas', '#marketing', '#business', '#growth'],
            lifestyle: ['#lifestyle', '#bemestar', '#equilibrio', '#mindfulness', '#qualidadedevida', '#inspiracao', '#vida']
        };
        
        return hashtags[niche.toLowerCase()] || hashtags.lifestyle;
    }

    generateBasicFallback(topic, tone, niche) {
        console.log('🔄 Usando fallback básico...');
        return `Hoje quero compartilhar sobre ${topic}! ✨\n\nEssa descoberta tem feito diferença real na minha jornada.\n\nPrincipais benefícios:\n• Resultados práticos\n• Fácil implementação\n• Impacto positivo\n\nQuem mais tem interesse nesse assunto? 💬\n\n${this.getHashtagsForNiche(niche).slice(0, 3).join(' ')}`;
    }
}

// Sistema de IA local para fallback (StoryAI)
class StoryAI {
    constructor() {
        this.templates = {
            casual: {
                fitness: [
                    "Gente, preciso falar sobre {topic}! 💪\n\nEsse assunto mudou completamente minha forma de ver o treino.\n\nO que mais me impressionou:\n✨ Resultados aparecem mais rápido\n✨ Treino fica mais prazeroso\n✨ Motivação se mantém alta\n\nQuem aqui já experimentou? Conta pra mim! 👇\n\n#fitness #treino #motivacao",
                    "Descobri algo incrível sobre {topic}! 🔥\n\nNão é só mais uma dica de treino, é uma mudança real de mindset.\n\nVocês precisam saber:\n🎯 Funciona mesmo\n🎯 É simples de aplicar\n🎯 Resultados são visíveis\n\nQuem topa testar comigo? 💪\n\n#fitness #motivacao #resultados"
                ],
                beleza: [
                    "Gente, {topic} tem sido meu segredo! ✨\n\nJá testei várias coisas, mas essa realmente fez diferença.\n\nO que mudou:\n💫 Autoestima lá em cima\n💫 Rotina mais prática\n💫 Confiança renovada\n\nQuem mais quer essa transformação? 🥰\n\n#beleza #autocuidado #glow",
                    "Preciso dividir essa descoberta sobre {topic}! 💄\n\nDepois que comecei, nunca mais parei. É viciante ver os resultados!\n\n✨ Simples de fazer\n✨ Cabe no orçamento\n✨ Efeito duradouro\n\nSalva aí e me conta depois! 💕\n\n#beleza #dicas #skincare"
                ],
                negocios: [
                    "Pessoal, {topic} mudou meu jogo! 🚀\n\nEra exatamente o que eu precisava para dar o próximo passo.\n\nResultados que vi:\n📈 Produtividade dobrou\n📈 Foco totalmente diferente\n📈 Metas sendo alcançadas\n\nQuem está pronto para essa mudança? 💼\n\n#empreendedorismo #produtividade #sucesso",
                    "Descoberta sobre {topic} que está transformando meu negócio! 💡\n\nNão é só teoria, é resultado prático mesmo.\n\n🎯 Estratégia clara\n🎯 Implementação simples\n🎯 ROI comprovado\n\nQuem quer saber mais? Comenta aqui! 👇\n\n#negocios #estrategia #crescimento"
                ],
                lifestyle: [
                    "Hoje quero falar sobre {topic}! 🌟\n\nEsse assunto tem me chamado muito a atenção e resolvi compartilhar com vocês.\n\nO que mais me impressiona é como pode impactar positivamente nossa rotina.\n\nVocês já tiveram experiência com isso? Contem nos comentários! 💬\n\n#lifestyle #inspiracao #vida",
                    "Gente, {topic} entrou na minha vida e mudou tudo! ✨\n\nNão é exagero, é real mesmo. A diferença é nítida.\n\n🌸 Bem-estar melhorou\n🌸 Energia renovada\n🌸 Equilíbrio encontrado\n\nQuem mais precisa dessa transformação? 🤗\n\n#lifestyle #bemestar #transformacao"
                ]
            },
            profissional: {
                fitness: [
                    "Análise técnica sobre {topic} 📊\n\nEstudos recentes demonstram eficácia comprovada desta abordagem.\n\nPrincipais evidências:\n• Melhoria de 40% na performance\n• Redução de 25% no tempo de recuperação\n• Aumento de 60% na aderência\n\nFonte: Pesquisa aplicada com 200 praticantes.\n\n#fitness #ciencia #evidencia",
                    "Protocolo científico: {topic} 🔬\n\nMetodologia validada por especialistas da área.\n\nParâmetros analisados:\n✓ Eficiência biomecânica\n✓ Resposta fisiológica\n✓ Adaptação neuromuscular\n\nResultados publicados em revista indexada.\n\n#cienciadoexercicio #protocolos #pesquisa"
                ],
                beleza: [
                    "Análise dermatológica: {topic} 🧴\n\nIngredientes ativos com comprovação científica.\n\nComposição técnica:\n• pH balanceado (5.5)\n• Concentração ativa otimizada\n• Estabilidade testada\n\nRecomendação profissional baseada em evidências.\n\n#dermatologia #skincare #ciencia",
                    "Avaliação científica de {topic} 🔬\n\nTestes laboratoriais confirmam:\n\n✓ Ativo principal: concentração ótima\n✓ Estabilidade: 24 meses testada\n✓ Biocompatibilidade: 99.7%\n\nClassificação: Cosmético de grau médico.\n\n#cosmetologia #qualidade #safety"
                ],
                negocios: [
                    "Análise estratégica: {topic} 📈\n\nDados do mercado confirmam tendência crescente.\n\nIndicadores-chave:\n• Market share: +15% YoY\n• ROI médio: 280%\n• Taxa de conversão: 8.5%\n\nOportunidade validada por métricas.\n\n#estrategia #dados #mercado",
                    "Case study: implementação de {topic} 💼\n\nResultados mensuráveis em 90 dias:\n\n📊 Revenue: +45%\n📊 Eficiência operacional: +30%\n📊 Customer satisfaction: 94%\n\nMetodologia replicável e escalável.\n\n#casestudy #resultados #performance"
                ],
                lifestyle: [
                    "Estudo comportamental: {topic} 📚\n\nPesquisa com 500 participantes revela impactos significativos.\n\nPrincipais achados:\n• Melhoria do bem-estar: 85%\n• Aumento da produtividade: 40%\n• Redução do stress: 60%\n\nDados coletados ao longo de 6 meses.\n\n#pesquisa #comportamento #lifestyle",
                    "Protocolo de implementação: {topic} 📋\n\nGuia baseado em metodologia científica.\n\nEtapas validadas:\n1. Diagnóstico inicial\n2. Planejamento estruturado\n3. Execução monitorada\n4. Avaliação de resultados\n\nEficácia comprovada em 95% dos casos.\n\n#protocolo #metodologia #resultados"
                ]
            },
            vendas: {
                fitness: [
                    "🔥 OPORTUNIDADE ÚNICA: {topic}!\n\nSó hoje com 50% OFF!\n\n⏰ Últimas 6 horas para garantir:\n✅ Método comprovado\n✅ Resultados em 30 dias\n✅ Garantia total\n\n👆 Link na bio AGORA!\n\n#oferta #fitness #oportunidade",
                    "⚡ REVELAÇÃO sobre {topic} que mudará sua vida!\n\nSegredo usado por atletas profissionais finalmente revelado!\n\n🎯 Acesso liberado hoje\n🎯 Vagas limitadas (restam 23)\n🎯 Bônus exclusivos inclusos\n\n🚀 GARANTE JÁ na bio!\n\n#revelacao #exclusivo #limitado"
                ],
                beleza: [
                    "💎 DESCOBERTA EXCLUSIVA: {topic}!\n\nO que influencers escondem de você!\n\n🔥 Hoje pela metade do preço:\n• Transformação garantida\n• Resultado em 7 dias\n• Aprovado por especialistas\n\n⏰ Só até meia-noite!\n👆 Bio para adquirir!\n\n#exclusivo #transformacao #limitado",
                    "✨ SEGREDO REVELADO: {topic}!\n\nDermatologistas não querem que você saiba!\n\n🌟 Oferta especial hoje:\n• 70% de desconto\n• Frete grátis\n• 3 bônus inclusos\n\n⚡ Últimas unidades!\n🔗 Link na bio!\n\n#segredo #oferta #urgente"
                ],
                negocios: [
                    "🚀 OPORTUNIDADE DE OURO: {topic}!\n\nMétodo que gera R$ 10k/mês finalmente revelado!\n\n💰 Liberado hoje com desconto:\n• Sistema completo\n• Suporte 24h\n• Garantia de resultado\n\n⏰ Vagas limitadas!\n👆 Acesso na bio!\n\n#oportunidade #renda #limitado",
                    "💎 REVELAÇÃO sobre {topic} que poucos sabem!\n\nSegredo usado pelos maiores especialistas:\n\n🎯 Técnica revolucionária\n🎯 Aplicação imediata\n🎯 Resultados exponenciais\n\n🔥 MASTERCLASS gratuita hoje às 20h!\n👆 Stories para se inscrever!\n\n#revelacao #masterclass #gratuito"
                ],
                lifestyle: [
                    "🌟 TRANSFORMAÇÃO TOTAL: {topic}!\n\nMudança que 95% das pessoas desconhece!\n\n✨ Acesso liberado hoje:\n• Método exclusivo\n• Resultados rápidos\n• Acompanhamento pessoal\n\n⚡ Promoção até sexta!\n🔗 Link na bio para garantir!\n\n#transformacao #exclusivo #promocao",
                    "🔥 DESCOBERTA que mudará sua vida: {topic}!\n\nO que coaches não contam sobre este método:\n\n💫 Disponível hoje com bônus:\n• Acesso vitalício\n• Comunidade VIP\n• Mentoria incluída\n\n⏰ Últimas horas!\n👆 Bio para adquirir!\n\n#descoberta #vitalicio #urgente"
                ]
            }
        };
    }    generateStory(topic, tone, niche) {
        try {
            console.log(`🤖 StoryAI - Gerando story local para: "${topic}", Tom: "${tone}", Nicho: "${niche}"`);
            
            // Mapeamento de tons para compatibilidade
            const toneMapping = {
                'sales': 'vendas',
                'professional': 'profissional',
                'casual': 'casual'
            };
            
            const normalizedTone = toneMapping[(tone || 'casual').toLowerCase().trim()] || (tone || 'casual').toLowerCase().trim();
            const normalizedNiche = (niche || 'lifestyle').toLowerCase().trim();
            
            // Verificar se existem templates para o tom e nicho especificados
            const toneTemplates = this.templates[normalizedTone];
            if (!toneTemplates || !toneTemplates[normalizedNiche]) {
                console.log(`⚠️ Template não encontrado para ${normalizedTone}/${normalizedNiche}, usando fallback`);
                return this.generateFallback(topic, normalizedTone, normalizedNiche);
            }
            
            const templates = toneTemplates[normalizedNiche];
            const randomIndex = Math.floor(Math.random() * templates.length);
            const selectedTemplate = templates[randomIndex];
            
            const story = selectedTemplate.replace(/{topic}/g, topic);
            
            console.log('✅ Story gerado com sucesso via StoryAI local!');
            return story;
            
        } catch (error) {
            console.error('❌ Erro na geração do story via StoryAI:', error);
            return this.generateFallback(topic, tone, niche);
        }
    }

    generateFallback(topic, tone, niche) {
        console.log('🔄 Usando fallback genérico da StoryAI...');
        const emojis = {
            fitness: '💪',
            beleza: '✨',
            negocios: '🚀',
            lifestyle: '🌟'
        };
        
        const emoji = emojis[niche] || '🌟';
        const hashtags = {
            fitness: '#fitness #treino #saude',
            beleza: '#beleza #autocuidado #skincare',
            negocios: '#empreendedorismo #negocios #sucesso',
            lifestyle: '#lifestyle #inspiracao #vida'
        };
        
        const hashtagSet = hashtags[niche] || '#inspiracao #vida #motivacao';
        
        return `Hoje vou falar sobre ${topic}! ${emoji}\n\nEsse assunto tem me chamado muito a atenção e resolvi compartilhar com vocês.\n\nO que mais me impressiona é como ${topic} pode impactar positivamente nossa rotina.\n\nVocês já tiveram experiência com isso? Contem nos comentários! 💬\n\n${hashtagSet}`;
    }
}

// Instância da IA
const githubAI = new GitHubAI();

// Carregar dados iniciais
let userData = loadData();

// Salvar dados periodicamente (backup automático)
setInterval(() => {
    saveData(userData);
}, 60000); // Salvar a cada minuto

// Instanciar o gerador de IA
const storyAI = new StoryAI();

// Base de dados para funcionalidades extras
const ctas = {
  vendas: [
    'Não perca esta oportunidade única!',
    'Últimas vagas disponíveis!',
    'Oferta válida por tempo limitado!',
    'Clique no link da bio e garante o seu!'
  ],
  engajamento: [
    'Comenta aqui embaixo o que você achou!',
    'Marca aquele amigo que precisa ver isso!',
    'Salva este post para não esquecer!',
    'Qual sua opinião sobre isso?'
  ],
  trafego: [
    'Link na bio para saber mais!',
    'Acesse nosso site e confira!',
    'Desliza para ver mais conteúdo!',
    'Toque no link dos destaques!'
  ]
};

const hashtags = {
  fitness: ['#fitness', '#treino', '#saude', '#motivacao', '#academia'],
  beleza: ['#beleza', '#skincare', '#makeup', '#autocuidado', '#dicas'],
  negocios: ['#empreendedorismo', '#negocios', '#sucesso', '#vendas', '#marketing'],
  lifestyle: ['#lifestyle', '#dicas', '#inspiracao', '#vida', '#bemestar']
};

const mentalTriggers = {
  escassez: [
    'Apenas hoje!',
    'Últimas unidades!',
    'Vagas limitadas!',
    'Por tempo limitado!'
  ],
  urgencia: [
    'Não deixe para depois!',
    'Agora ou nunca!',
    'Tempo acabando!',
    'Últimas horas!'
  ],
  reciprocidade: [
    'Especialmente para você!',
    'Um presente de agradecimento!',
    'Retribuindo seu carinho!',
    'Como forma de gratidão!'
  ]
};

const weeklyCalendar = {
  segunda: { theme: 'Motivação Monday', tip: 'Comece a semana inspirando seus seguidores' },
  terca: { theme: 'Tutorial Tuesday', tip: 'Ensine algo novo e útil' },
  quarta: { theme: 'Wisdom Wednesday', tip: 'Compartilhe conhecimento e experiências' },
  quinta: { theme: 'Throwback Thursday', tip: 'Conte uma história do passado' },
  sexta: { theme: 'Feel Good Friday', tip: 'Celebre conquistas e momentos positivos' },
  sabado: { theme: 'Saturday Stories', tip: 'Seja mais descontraído e pessoal' },
  domingo: { theme: 'Sunday Reflection', tip: 'Reflita sobre a semana e planeje a próxima' }
};

const reelsIdeas = [
  'Antes e depois transformação',
  'Dica rápida em 15 segundos',
  'Erro comum que você deve evitar',
  'Minha rotina matinal',
  'Produto que mudou minha vida',
  '3 segredos que ninguém conta',
  'Pergunta e resposta com seguidores',
  'Bastidores do meu trabalho',
  'Comparação: expectativa vs realidade',
  'Tutorial passo a passo'
];

// Rotas principais
app.get('/', (req, res) => {
  res.render('dashboard', { 
    user: userData.user, 
    weeklyChallenge: userData.weeklyChallenge,
    calendar: weeklyCalendar,
    recentHistory: userData.history.slice(-5)
  });
});

app.get('/ferramentas-extras', (req, res) => {
  res.render('extras', { 
    ctas, 
    hashtags, 
    mentalTriggers, 
    reelsIdeas,
    user: userData.user 
  });
});

app.get('/historico', (req, res) => {
  res.render('history', { 
    history: userData.history, 
    favorites: userData.favorites,
    user: userData.user 
  });
});

app.get('/gerador', (req, res) => {
  res.render('generator', { user: userData.user });
});

// API endpoints
app.post('/api/generate-story', (req, res) => {
  const { topic, tone, niche } = req.body;
  
  console.log('📝 Recebida solicitação de geração:', { topic, tone, niche });
  
  // Validar entrada
  if (!topic || !tone || !niche) {
    console.log('❌ Dados incompletos recebidos');
    return res.status(400).json({ 
      success: false, 
      message: 'Dados incompletos. Topic, tone e niche são obrigatórios.' 
    });
  }
  
  try {
    // Gerar conteúdo usando IA
    console.log('🤖 Iniciando geração com IA...');
    const generatedContent = storyAI.generateStory(topic, tone, niche);
    console.log('✅ Conteúdo gerado:', generatedContent.substring(0, 100) + '...');
    
    const story = {
      id: uuidv4(),
      topic: topic.trim(),
      tone: tone.trim(),
      niche: niche.trim(),
      content: generatedContent,
      createdAt: moment().format('DD/MM/YYYY HH:mm'),
      isFavorite: false
    };
    
    // Adicionar ao histórico
    userData.history.unshift(story);
    userData.user.stats.storiesGenerated++;
    
    // Atualizar desafio semanal
    if (userData.weeklyChallenge.progress < userData.weeklyChallenge.target) {
      userData.weeklyChallenge.progress++;
      if (userData.weeklyChallenge.progress === userData.weeklyChallenge.target) {
        userData.weeklyChallenge.completed = true;
      }
    }
    
    // Salvar dados
    const saveSuccess = saveData(userData);
    if (!saveSuccess) {
      console.error('❌ Erro ao salvar dados após gerar story');
    } else {
      console.log('💾 Dados salvos com sucesso');
    }
    
    console.log('🎉 Story gerado e salvo com sucesso!');
    res.json({ success: true, story });
    
  } catch (error) {
    console.error('❌ Erro ao gerar story:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Erro interno do servidor ao gerar story' 
    });
  }
});

app.post('/api/toggle-favorite/:id', (req, res) => {
  const storyId = req.params.id;
  const story = userData.history.find(s => s.id === storyId);
  
  if (story) {
    story.isFavorite = !story.isFavorite;
    
    if (story.isFavorite) {
      // Verificar se já não está nos favoritos
      const existingFav = userData.favorites.find(f => f.id === storyId);
      if (!existingFav) {
        userData.favorites.push(story);
        userData.user.stats.favoritesCount++;
      }
    } else {
      userData.favorites = userData.favorites.filter(f => f.id !== storyId);
      userData.user.stats.favoritesCount = Math.max(0, userData.user.stats.favoritesCount - 1);
    }
    
    // Salvar dados
    const saveSuccess = saveData(userData);
    if (!saveSuccess) {
      console.error('Erro ao salvar dados após alterar favorito');
    }
    
    res.json({ success: true, isFavorite: story.isFavorite });
  } else {
    res.status(404).json({ success: false, message: 'Story não encontrado' });
  }
});

app.get('/api/hashtags/:niche', (req, res) => {
  const niche = req.params.niche.toLowerCase();
  const tags = hashtags[niche] || hashtags.lifestyle;
  res.json({ hashtags: tags });
});

app.get('/api/user-stats', (req, res) => {
  res.json({ stats: userData.user.stats, challenge: userData.weeklyChallenge });
});

// Iniciar servidor
app.listen(PORT, () => {
  console.log(`🚀 Servidor rodando na porta ${PORT}`);
  console.log(`🌐 Acesse: http://localhost:${PORT}`);
  console.log(`🤖 IA de geração de stories carregada com sucesso!`);
  console.log(`📊 Dados de usuário carregados:`, userData.user.stats);
});
