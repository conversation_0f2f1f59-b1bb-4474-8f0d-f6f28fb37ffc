<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Meu Histórico - Stories Generator</title>
    <link rel="stylesheet" href="/css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-video"></i>
                Stories Generator
            </div>
            <div class="nav-upgrade">
                <a href="#" class="upgrade-btn">
                    <i class="fas fa-crown"></i>
                    Economize 30% - Plano Anual
                </a>
            </div>
        </div>
    </nav>

    <div class="container">
        <aside class="sidebar">
            <ul class="nav-menu">
                <li><a href="/"><i class="fas fa-home"></i> Dashboard</a></li>
                <li><a href="/gerador"><i class="fas fa-magic"></i> Gerar Story</a></li>
                <li><a href="/ferramentas-extras"><i class="fas fa-tools"></i> Ferramentas Extras</a></li>
                <li><a href="/historico" class="active"><i class="fas fa-history"></i> Meu Histórico</a></li>
            </ul>
        </aside>

        <main class="main-content">
            <div class="page-header">
                <h1>📈 Meu Histórico</h1>
                <p>Seus roteiros criados e favoritos em um só lugar</p>
            </div>

            <!-- Filter Tabs -->
            <div class="filter-tabs">
                <button class="tab-btn active" onclick="showTab('all')">
                    <i class="fas fa-list"></i> Todos (<%= history.length %>)
                </button>
                <button class="tab-btn" onclick="showTab('favorites')">
                    <i class="fas fa-heart"></i> Favoritos (<%= favorites.length %>)
                </button>
            </div>

            <!-- All History -->
            <div class="history-content" id="tab-all">
                <% if (history && history.length > 0) { %>
                    <div class="history-grid">
                        <% history.forEach(item => { %>                            <div class="history-card">
                                <div class="card-header">
                                    <div class="card-title">
                                        <h3><%= item.topic %></h3>
                                        <% if (item.tone) { %>
                                            <span class="tone-badge tone-<%= item.tone.toLowerCase().replace(/\s+/g, '-') %>">
                                                <%= item.tone %>
                                            </span>
                                        <% } else { %>
                                            <span class="tone-badge tone-casual">
                                                Casual
                                            </span>
                                        <% } %>
                                    </div>
                                    <div class="card-actions">
                                        <button class="btn-icon" onclick="toggleFavorite('<%= item.id %>')" 
                                                title="<%= item.isFavorite ? 'Remover dos favoritos' : 'Adicionar aos favoritos' %>">
                                            <i class="<%= item.isFavorite ? 'fas' : 'far' %> fa-heart"></i>
                                        </button>
                                        <button class="btn-icon" onclick="regenerateStory('<%= item.id %>')" title="Usar de novo">
                                            <i class="fas fa-redo"></i>
                                        </button>
                                        <button class="btn-icon" onclick="copyStory('<%= item.id %>')" title="Copiar conteúdo">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="card-content">
                                    <div class="story-preview">
                                        <%= item.content.length > 150 ? item.content.substring(0, 150) + '...' : item.content %>
                                    </div>
                                    <div class="story-meta">
                                        <span class="meta-item">
                                            <i class="fas fa-calendar"></i>
                                            <%= item.createdAt %>
                                        </span>
                                        <span class="meta-item">
                                            <i class="fas fa-tag"></i>
                                            <%= item.niche %>
                                        </span>
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <button class="btn-secondary btn-sm" onclick="viewFullStory('<%= item.id %>')">
                                        Ver Completo
                                    </button>
                                    <button class="btn-primary btn-sm" onclick="regenerateStory('<%= item.id %>')">
                                        <i class="fas fa-magic"></i>
                                        Usar de Novo
                                    </button>
                                </div>
                            </div>
                        <% }) %>
                    </div>
                <% } else { %>
                    <div class="empty-state">
                        <i class="fas fa-history"></i>
                        <h3>Nenhum histórico ainda</h3>
                        <p>Seus roteiros criados aparecerão aqui</p>
                        <a href="/gerador" class="btn-primary">
                            <i class="fas fa-plus"></i>
                            Criar Primeiro Story
                        </a>
                    </div>
                <% } %>
            </div>

            <!-- Favorites -->
            <div class="history-content" id="tab-favorites" style="display: none;">
                <% if (favorites && favorites.length > 0) { %>
                    <div class="history-grid">
                        <% favorites.forEach(item => { %>
                            <div class="history-card favorite-card">
                                <div class="favorite-badge">
                                    <i class="fas fa-heart"></i>
                                </div>                                <div class="card-header">
                                    <div class="card-title">
                                        <h3><%= item.topic %></h3>
                                        <% if (item.tone) { %>
                                            <span class="tone-badge tone-<%= item.tone.toLowerCase().replace(/\s+/g, '-') %>">
                                                <%= item.tone %>
                                            </span>
                                        <% } else { %>
                                            <span class="tone-badge tone-casual">
                                                Casual
                                            </span>
                                        <% } %>
                                    </div>
                                    <div class="card-actions">
                                        <button class="btn-icon" onclick="toggleFavorite('<%= item.id %>')" title="Remover dos favoritos">
                                            <i class="fas fa-heart"></i>
                                        </button>
                                        <button class="btn-icon" onclick="regenerateStory('<%= item.id %>')" title="Usar de novo">
                                            <i class="fas fa-redo"></i>
                                        </button>
                                        <button class="btn-icon" onclick="copyStory('<%= item.id %>')" title="Copiar conteúdo">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="card-content">
                                    <div class="story-preview">
                                        <%= item.content.length > 150 ? item.content.substring(0, 150) + '...' : item.content %>
                                    </div>
                                    <div class="story-meta">
                                        <span class="meta-item">
                                            <i class="fas fa-calendar"></i>
                                            <%= item.createdAt %>
                                        </span>
                                        <span class="meta-item">
                                            <i class="fas fa-tag"></i>
                                            <%= item.niche %>
                                        </span>
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <button class="btn-secondary btn-sm" onclick="viewFullStory('<%= item.id %>')">
                                        Ver Completo
                                    </button>
                                    <button class="btn-primary btn-sm" onclick="regenerateStory('<%= item.id %>')">
                                        <i class="fas fa-magic"></i>
                                        Usar de Novo
                                    </button>
                                </div>
                            </div>
                        <% }) %>
                    </div>
                <% } else { %>
                    <div class="empty-state">
                        <i class="fas fa-heart"></i>
                        <h3>Nenhum favorito ainda</h3>
                        <p>Adicione stories aos favoritos clicando no ❤️</p>
                        <a href="/" class="btn-primary">
                            <i class="fas fa-arrow-left"></i>
                            Voltar ao Dashboard
                        </a>
                    </div>
                <% } %>
            </div>
        </main>
    </div>

    <!-- Modal para visualizar story completo -->
    <div class="modal" id="storyModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">Story Completo</h2>
                <button class="modal-close" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div id="modalContent"></div>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" onclick="closeModal()">Fechar</button>
                <button class="btn-primary" onclick="copyModalContent()">
                    <i class="fas fa-copy"></i>
                    Copiar Conteúdo
                </button>
            </div>
        </div>
    </div>

    <script src="/js/history.js"></script>
</body>
</html>
