/* Reset e configurações base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background-color: #f8fafc;
    color: #334155;
    line-height: 1.6;
}

/* Navbar */
.navbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 1rem;
}

.nav-brand {
    font-size: 1.5rem;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.upgrade-btn {
    background: rgba(255,255,255,0.2);
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    border: 1px solid rgba(255,255,255,0.3);
}

.upgrade-btn:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-1px);
}

/* Container Layout */
.container {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 250px 1fr;
    gap: 2rem;
    padding: 2rem 1rem;
    min-height: calc(100vh - 80px);
}

/* Sidebar */
.sidebar {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    height: fit-content;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
    position: sticky;
    top: 100px;
}

.nav-menu {
    list-style: none;
}

.nav-menu li {
    margin-bottom: 0.5rem;
}

.nav-menu a {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    border-radius: 8px;
    text-decoration: none;
    color: #64748b;
    transition: all 0.3s ease;
    font-weight: 500;
}

.nav-menu a:hover,
.nav-menu a.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: translateX(4px);
}

/* Main Content */
.main-content {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
}

/* Welcome Section */
.welcome-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    color: white;
}

.welcome-text h1 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.welcome-text p {
    opacity: 0.9;
    font-size: 1.1rem;
}

/* Buttons */
.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: #f1f5f9;
    color: #475569;
    border: 1px solid #e2e8f0;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: #e2e8f0;
    transform: translateY(-1px);
}

.btn-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
}

.btn-link:hover {
    text-decoration: underline;
}

.btn-icon {
    background: none;
    border: none;
    padding: 0.5rem;
    border-radius: 6px;
    cursor: pointer;
    color: #64748b;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.btn-icon:hover {
    background: #f1f5f9;
    color: #667eea;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
}

.stat-card:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.stat-content h3 {
    font-size: 2rem;
    font-weight: bold;
    color: #1e293b;
    margin-bottom: 0.25rem;
}

.stat-content p {
    color: #64748b;
    font-size: 0.9rem;
}

/* Challenge Card */
.challenge-card {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    border-radius: 12px;
    padding: 2rem;
    color: white;
    margin-bottom: 2rem;
}

.challenge-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.challenge-header h2 {
    font-size: 1.5rem;
}

.challenge-badge {
    background: rgba(255,255,255,0.2);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.challenge-content p {
    font-size: 1.1rem;
    margin-bottom: 1rem;
    opacity: 0.95;
}

.progress-bar {
    background: rgba(255,255,255,0.2);
    height: 8px;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.75rem;
}

.progress-fill {
    background: white;
    height: 100%;
    border-radius: 4px;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 0.9rem;
    opacity: 0.9;
    margin-bottom: 1rem;
}

.challenge-completed {
    background: rgba(255,255,255,0.2);
    padding: 1rem;
    border-radius: 8px;
    text-align: center;
    font-weight: 600;
}

.challenge-motivation {
    text-align: center;
    font-weight: 600;
    opacity: 0.9;
}

/* Calendar Widget */
.calendar-widget {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.widget-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.widget-header h2 {
    color: #1e293b;
    font-size: 1.3rem;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.calendar-day {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.calendar-day:hover {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
}

.day-name {
    font-weight: 600;
    font-size: 0.9rem;
    text-transform: capitalize;
    margin-bottom: 0.5rem;
}

.day-theme {
    font-size: 0.8rem;
    opacity: 0.8;
}

/* Activity Section */
.recent-activity {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 1.5rem;
}

.activity-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.activity-item:hover {
    background: #f1f5f9;
}

.activity-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.25rem;
}

.activity-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.85rem;
    color: #64748b;
}

.activity-tone {
    background: #e2e8f0;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
}

.activity-actions {
    display: flex;
    gap: 0.5rem;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #64748b;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: #1e293b;
}

.empty-state p {
    margin-bottom: 2rem;
}

/* Page Header */
.page-header {
    margin-bottom: 2rem;
}

.page-header h1 {
    font-size: 2.5rem;
    color: #1e293b;
    margin-bottom: 0.5rem;
}

.page-header p {
    font-size: 1.1rem;
    color: #64748b;
}

/* Tool Sections */
.tool-section {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.tool-header {
    margin-bottom: 2rem;
}

.tool-header h2 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.5rem;
    color: #1e293b;
    margin-bottom: 0.5rem;
}

.tool-header p {
    color: #64748b;
}

/* CTA Categories */
.category-tabs {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
}

.tab-btn {
    background: none;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px 8px 0 0;
    cursor: pointer;
    font-weight: 600;
    color: #64748b;
    transition: all 0.3s ease;
}

.tab-btn.active,
.tab-btn:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.cta-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.cta-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.cta-text {
    font-style: italic;
    color: #1e293b;
    flex: 1;
}

.copy-btn {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.copy-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

/* Hashtag Generator */
.hashtag-generator {
    background: #f8fafc;
    border-radius: 8px;
    padding: 1.5rem;
}

.input-group {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.input-group select {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 1rem;
}

.hashtag-results {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.hashtag-tag {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.hashtag-tag:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* Reels Grid */
.reels-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1rem;
}

.reel-idea-card {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 1px solid #d1d5db;
    border-radius: 12px;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.reel-idea-card:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.reel-idea-card:hover .copy-indicator {
    opacity: 1;
}

.idea-number {
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 30px;
    height: 30px;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.9rem;
}

.idea-text {
    font-weight: 600;
    font-size: 1.1rem;
    line-height: 1.4;
    padding-right: 2rem;
}

.copy-indicator {
    position: absolute;
    bottom: 1rem;
    right: 1rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.renewal-badge {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

/* Triggers Container */
.triggers-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.trigger-category {
    background: #f8fafc;
    border-radius: 12px;
    padding: 1.5rem;
}

.trigger-title {
    color: #1e293b;
    font-size: 1.2rem;
    margin-bottom: 1rem;
    text-transform: capitalize;
    border-bottom: 2px solid #667eea;
    padding-bottom: 0.5rem;
}

.trigger-phrases {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.phrase-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: white;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.phrase-item:hover {
    border-left-color: #667eea;
    background: #f1f5f9;
    transform: translateX(4px);
}

/* Filter Tabs (History) */
.filter-tabs {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    border-bottom: 1px solid #e2e8f0;
    padding-bottom: 1rem;
}

/* History Grid */
.history-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
}

.history-card {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
}

.history-card:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.favorite-card {
    border-color: #f59e0b;
    background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
}

.favorite-badge {
    position: absolute;
    top: -8px;
    right: 1rem;
    background: #f59e0b;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.card-title h3 {
    color: #1e293b;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
}

.tone-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    background: #e2e8f0;
    color: #475569;
}

.tone-casual { background: #dbeafe; color: #1e40af; }
.tone-professional { background: #f3e8ff; color: #7c3aed; }
.tone-sales,
.tone-focado-em-conversão-vendas { background: #fecaca; color: #dc2626; }

.card-actions {
    display: flex;
    gap: 0.5rem;
}

.story-preview {
    background: #f8fafc;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    line-height: 1.5;
    border-left: 3px solid #667eea;
}

.story-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.8rem;
    color: #64748b;
}

.card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    backdrop-filter: blur(4px);
}

.modal-content {
    position: relative;
    background: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 12px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 2rem;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
}

.modal-body {
    padding: 2rem;
    max-height: 400px;
    overflow-y: auto;
}

.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid #e2e8f0;
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

/* Generator Styles */
.generator-container {
    max-width: 600px;
    margin: 0 auto;
}

.generator-form {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.form-step {
    display: none;
}

.form-step.active {
    display: block;
}

.step-header {
    text-align: center;
    margin-bottom: 2rem;
}

.step-header h2 {
    color: #1e293b;
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.step-header p {
    color: #64748b;
}

.form-group {
    margin-bottom: 2rem;
}

.form-group label {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group small {
    color: #6b7280;
    font-size: 0.85rem;
    margin-top: 0.25rem;
    display: block;
}

.tone-options {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.tone-option {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tone-option:hover,
.tone-option.selected {
    border-color: #667eea;
    background: #f8fafc;
}

.tone-icon {
    font-size: 2rem;
    width: 60px;
    text-align: center;
}

.tone-content {
    flex: 1;
}

.tone-content h3 {
    color: #1f2937;
    font-size: 1.1rem;
    margin-bottom: 0.25rem;
}

.tone-content p {
    color: #6b7280;
    font-size: 0.9rem;
}

.tone-radio input {
    margin: 0;
}

.step-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 2rem;
}

.progress-container {
    text-align: center;
    margin-bottom: 2rem;
}

.progress-container .progress-bar {
    background: #e5e7eb;
    height: 4px;
    border-radius: 2px;
    margin-bottom: 1rem;
    overflow: hidden;
}

.progress-container .progress-fill {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    height: 100%;
    transition: width 0.3s ease;
}

.progress-steps {
    display: flex;
    justify-content: center;
    gap: 2rem;
}

.progress-step {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #e5e7eb;
    color: #6b7280;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    transition: all 0.3s ease;
}

.progress-step.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

/* Loading State */
.loading-container {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
}

.loading-content {
    max-width: 400px;
    margin: 0 auto;
}

.loading-spinner {
    position: relative;
    width: 80px;
    height: 80px;
    margin: 0 auto 2rem;
}

.spinner-ring {
    position: absolute;
    width: 100%;
    height: 100%;
    border: 3px solid transparent;
    border-radius: 50%;
    animation: spin 2s linear infinite;
}

.spinner-ring:nth-child(1) {
    border-top-color: #667eea;
    animation-delay: 0s;
}

.spinner-ring:nth-child(2) {
    border-right-color: #764ba2;
    animation-delay: 0.3s;
}

.spinner-ring:nth-child(3) {
    border-bottom-color: #10b981;
    animation-delay: 0.6s;
}

.loading-progress {
    background: #e5e7eb;
    height: 6px;
    border-radius: 3px;
    overflow: hidden;
    margin: 1.5rem 0;
}

.loading-progress .progress-bar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    height: 100%;
    border-radius: 3px;
    transition: width 0.3s ease;
}

.loading-container h3 {
    color: #1e293b;
    margin-bottom: 0.5rem;
    font-size: 1.5rem;
}

.loading-container p {
    color: #667eea;
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.loading-container small {
    color: #64748b;
    font-size: 0.9rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Result Container */
.result-container {
    max-width: 600px;
    margin: 2rem auto;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    overflow: hidden;
}

.result-header {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.result-header h2 {
    font-size: 1.5rem;
}

.result-actions {
    display: flex;
    gap: 0.5rem;
}

.result-actions .btn-icon {
    background: rgba(255,255,255,0.2);
    color: white;
}

.result-actions .btn-icon:hover {
    background: rgba(255,255,255,0.3);
}

.result-actions .btn-secondary {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
}

.result-content {
    padding: 2rem;
}

.story-output {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 1.5rem;
    font-size: 1.1rem;
    line-height: 1.6;
    white-space: pre-wrap;
}

.result-footer {
    padding: 1.5rem;
    border-top: 1px solid #e2e8f0;
    background: #f8fafc;
}

.result-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.meta-tag {
    background: #667eea;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
}

.result-cta {
    text-align: center;
    color: #64748b;
}

.result-cta a {
    color: #667eea;
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 1rem;
    }
    
    .sidebar {
        position: static;
        order: 1;
    }
    
    .main-content {
        order: 0;
        padding: 1rem;
    }
    
    .nav-container {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .upgrade-btn {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }
    
    .welcome-section {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
    
    .welcome-text h1 {
        font-size: 1.5rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .calendar-grid {
        grid-template-columns: 1fr;
    }
    
    .challenge-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .page-header h1 {
        font-size: 2rem;
    }
    
    .history-grid {
        grid-template-columns: 1fr;
    }
    
    .reels-grid {
        grid-template-columns: 1fr;
    }
    
    .triggers-container {
        grid-template-columns: 1fr;
    }
    
    .input-group {
        flex-direction: column;
    }
    
    .step-actions {
        flex-direction: column;
        gap: 1rem;
    }
    
    .result-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .modal-content {
        margin: 10% auto;
        width: 95%;
    }
    
    .card-footer {
        flex-direction: column;
        gap: 0.5rem;
    }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 2rem; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 2rem; }

/* Hover Effects */
.hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

/* Success Messages */
.success-message {
    background: #d1fae5;
    color: #065f46;
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid #10b981;
    margin: 1rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.error-message {
    background: #fee2e2;
    color: #991b1b;
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid #ef4444;
    margin: 1rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}
